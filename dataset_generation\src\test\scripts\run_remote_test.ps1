# Parameters
$remoteUser = "user"
$remoteHost = "jtmksrv"
$remotePort = 656
$remotePassword = "1"
$remoteDir = "/home/<USER>/dataset/test_run"

# Create a temporary directory
$tempDir = "$env:TEMP\sdn_test_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
New-Item -ItemType Directory -Path $tempDir -Force | Out-Null

# Copy the entire project directory
$excludeDirs = @(".git", "__pycache__", ".pytest_cache", "test_reports", "logs", "venv", ".vscode", ".idea")

Get-ChildItem -Path . -Recurse | Where-Object {
    $include = $true
    foreach ($dir in $excludeDirs) {
        if ($_.FullName -match [regex]::Escape($dir)) {
            $include = $false
            break
        }
    }
    return $include
} | Copy-Item -Destination {
    $destPath = $_.FullName.Replace((Get-Location).Path, $tempDir)
    if (-not $_.PSIsContainer) {
        $destDir = [System.IO.Path]::GetDirectoryName($destPath)
        if (-not (Test-Path $destDir)) {
            New-Item -ItemType Directory -Path $destDir -Force | Out-Null
        }
    }
    $destPath
} -Force -Recurse

# Create a zip file
$zipPath = "$tempDir\sdn_project.zip"
Compress-Archive -Path "$tempDir\*" -DestinationPath $zipPath -Force

# Transfer the zip file to the remote server
try {
    # Using SCP to transfer the file
    $scpCommand = "scp -P $remotePort \"$zipPath\" ${remoteUser}@${remoteHost}:/tmp/"
    $scpProcess = Start-Process -FilePath "powershell" -ArgumentList "-Command", $scpCommand -NoNewWindow -Wait -PassThru -RedirectStandardOutput "$tempDir\scp_stdout.log" -RedirectStandardError "$tempDir\scp_stderr.log"
    
    if ($scpProcess.ExitCode -ne 0) {
        Write-Host "SCP transfer failed. Check $tempDir\scp_*.log for details."
        exit 1
    }
    
    # SSH commands to run on the remote server
    $sshCommands = @(
        "mkdir -p $remoteDir",
        "unzip -q -o /tmp/sdn_project.zip -d $remoteDir",
        "cd $remoteDir",
        "python3 -m venv venv",
        "source venv/bin/activate",
        "pip install -r requirements.txt",
        "python -m pytest src/test/ -v"
    ) -join "; "
    
    # Execute commands remotely
    $sshCommand = "ssh -p $remotePort ${remoteUser}@${remoteHost} \"$sshCommands\""
    $sshProcess = Start-Process -FilePath "powershell" -ArgumentList "-Command", $sshCommand -NoNewWindow -Wait -PassThru -RedirectStandardOutput "$tempDir\ssh_stdout.log" -RedirectStandardError "$tempDir\ssh_stderr.log"
    
    # Display the output
    Get-Content "$tempDir\ssh_stdout.log"
    Get-Content "$tempDir\ssh_stderr.log"
    
    if ($sshProcess.ExitCode -ne 0) {
        Write-Host "Remote command execution failed with exit code $($sshProcess.ExitCode)"
    }
    
} catch {
    Write-Host "An error occurred: $_"
} finally {
    # Cleanup
    # Remove-Item -Path $tempDir -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "Temporary files kept at: $tempDir"
}
