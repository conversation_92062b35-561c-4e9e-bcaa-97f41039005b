2025-07-08 22:05:33,313 - INFO - Starting simple test...
2025-07-08 22:05:33,313 - INFO - Checking environment...
2025-07-08 22:05:33,313 - ERROR - Required directory not found: attacks
2025-07-08 22:05:33,313 - ERROR - Environment check failed. Please fix the issues and try again.
2025-07-08 22:18:36,532 - INFO - Starting simple test...
2025-07-08 22:18:36,532 - INFO - Checking environment...
2025-07-08 22:18:36,533 - INFO - Environment check passed
2025-07-08 22:18:36,533 - INFO - Created test configuration file: test_config.json
2025-07-08 22:18:36,533 - INFO - Test setup complete. You can now run the test with:
2025-07-08 22:18:36,534 - INFO - sudo python3 main.py test_config.json
2025-07-08 22:18:41,689 - INFO - Starting simple test...
2025-07-08 22:18:41,689 - INFO - Checking environment...
2025-07-08 22:18:41,690 - INFO - Environment check passed
2025-07-08 22:18:41,691 - INFO - Created test configuration file: test_config.json
2025-07-08 22:18:41,691 - INFO - Test setup complete. You can now run the test with:
2025-07-08 22:18:41,691 - INFO - sudo python3 main.py test_config.json
2025-07-08 22:19:09,406 - INFO - Starting simple test...
2025-07-08 22:19:09,406 - INFO - Checking environment...
2025-07-08 22:19:09,407 - INFO - Environment check passed
2025-07-08 22:19:09,407 - INFO - Created test configuration file: test_config.json
2025-07-08 22:19:09,407 - INFO - Test setup complete. You can now run the test with:
2025-07-08 22:19:09,407 - INFO - sudo python3 main.py test_config.json
