<#
.SYNOPSIS
    Run remote tests for AdDDoSDN Dataset Generation
.DESCRIPTION
    This script runs remote tests using Posh-SSH to connect to the test server.
    It requires Posh-SSH module to be installed.
#>

param(
    [string]$RemoteHost = "jtmksrv",
    [int]$Port = 656,
    [string]$Username = "user",
    [string]$Password = "1",
    [string]$RemoteDir = "/home/<USER>/dataset/test_run"
)

# Function to check if Posh-SSH is installed
function Test-PoshSSHInstalled {
    return (Get-Module -ListAvailable -Name Posh-SSH) -ne $null
}

# Function to install Posh-SSH
function Install-PoshSSH {
    try {
        Write-Host "Installing Posh-SSH module..."
        Install-Module -Name Posh-SSH -Force -Scope CurrentUser -AllowClobber
        Import-Module Posh-SSH -Force
        return $true
    } catch {
        Write-Error "Failed to install Posh-SSH: $_"
        return $false
    }
}

# Main script execution
try {
    # Check if Posh-SSH is installed
    if (-not (Test-PoshSSHInstalled)) {
        Write-Host "Posh-SSH module not found. Attempting to install..."
        if (-not (Install-PoshSSH)) {
            Write-Error "Posh-SSH is required for remote tests. Please install it manually and try again."
            exit 1
        }
    } else {
        Import-Module Posh-SSH -Force
    }

    # Create credential object
    $securePassword = ConvertTo-SecureString $Password -AsPlainText -Force
    $credential = New-Object System.Management.Automation.PSCredential($Username, $securePassword)

    # Create SSH session
    Write-Host "Connecting to $RemoteHost..."
    $session = New-SSHSession -ComputerName $RemoteHost -Port $Port -Credential $credential -AcceptKey -Force

    if (-not $session.Connected) {
        Write-Error "Failed to connect to $RemoteHost"
        exit 1
    }

    try {
        # Commands to run on the remote server
        $commands = @(
            "cd $RemoteDir",
            "source venv/bin/activate",
            "python -m pytest src/test/e2e/ -v"
        ) -join "; "

        # Execute commands
        Write-Host "Running remote tests..."
        $result = Invoke-SSHCommand -SSHSession $session -Command $commands

        # Display output
        $result.Output | ForEach-Object { Write-Host $_ }

        # Check for errors
        if ($result.ExitStatus -ne 0) {
            Write-Error "Remote tests failed with exit code $($result.ExitStatus)"
            if ($result.Error) {
                Write-Error "Error output:"
                $result.Error | ForEach-Object { Write-Error $_ }
            }
            exit 1
        }

        Write-Host "✅ Remote tests completed successfully"
        exit 0
    }
    finally {
        # Clean up the session
        if ($session -and $session.Connected) {
            Remove-SSHSession -SSHSession $session | Out-Null
        }
    }
}
catch {
    Write-Error "An error occurred: $_"
    Write-Error $_.ScriptStackTrace
    exit 1
}
