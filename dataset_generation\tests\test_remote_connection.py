#!/usr/bin/env python3
"""
Test script to verify SSH connection to the remote server.
"""
import sys
import os
from pathlib import Path

# Add parent directory to path to import remote_exec
sys.path.append(str(Path(__file__).parent))
from src.remote_exec import Remote<PERSON><PERSON>

def test_connection():
    """Test SSH connection to the remote server."""
    hostname = 'jtmksrv'  # Replace with actual test server
    port = 656
    username = 'user'     # Replace with test username
    password = '1'        # Replace with test password
    
    print(f"Testing connection to {username}@{hostname}:{port}...")
    
    # Create client
    client = RemoteCLI(
        hostname=hostname,
        port=port,
        username=username,
        password=password
    )
    
    try:
        # Test connection
        print("Attempting to connect...")
        if client.connect():
            print("✓ Connection successful!")
            
            # Test command execution
            print("\nTesting command execution...")
            command = "echo 'Hello, World!'"
            print(f"Executing: {command}")
            
            stdout, stderr, exit_code = client.execute_command(command)
            print(f"Exit code: {exit_code}")
            print(f"STDOUT: {stdout}")
            if stderr:
                print(f"STDERR: {stderr}")
                
            if exit_code == 0 and "Hello, World!" in stdout:
                print("✓ Command execution successful!")
            else:
                print("✗ Command execution failed!")
        else:
            print("✗ Connection failed!")
            
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        if hasattr(client, 'ssh') and client.ssh:
            client.close()
            print("\nConnection closed.")

if __name__ == "__main__":
    test_connection()
