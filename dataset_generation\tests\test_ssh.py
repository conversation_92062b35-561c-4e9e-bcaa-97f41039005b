#!/usr/bin/env python3
"""
Simple script to test SSH connection using paramiko directly.
"""
import paramiko
import sys

def test_ssh_connection(hostname, port, username, password):
    """Test SSH connection using paramiko."""
    print(f"Testing SSH connection to {username}@{hostname}:{port}...")
    
    # Create SSH client
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        print("Attempting to connect...")
        ssh.connect(
            hostname=hostname,
            port=port,
            username=username,
            password=password,
            timeout=10,
            banner_timeout=30,
            auth_timeout=10,
            allow_agent=False,
            look_for_keys=False
        )
        print("✓ SSH Connection successful!")
        
        # Test command execution
        print("\nTesting command execution...")
        stdin, stdout, stderr = ssh.exec_command("echo 'Hello, World!'")
        exit_code = stdout.channel.recv_exit_status()
        
        print(f"Exit code: {exit_code}")
        print(f"STDOUT: {stdout.read().decode().strip()}")
        
        stderr_output = stderr.read().decode().strip()
        if stderr_output:
            print(f"STDERR: {stderr_output}")
        
        if exit_code == 0:
            print("✓ Command execution successful!")
        else:
            print("✗ Command execution failed!")
            
    except paramiko.AuthenticationException as e:
        print(f"✗ Authentication failed: {str(e)}")
        print("Please verify the username and password.")
    except paramiko.SSHException as e:
        print(f"✗ SSH connection failed: {str(e)}")
        if "Error reading SSH protocol banner" in str(e):
            print("This might be due to network issues or the server not being reachable.")
    except Exception as e:
        print(f"✗ An error occurred: {str(e)}")
    finally:
        ssh.close()
        print("\nConnection closed.")

if __name__ == "__main__":
    # Test connection parameters
    hostname = 'jtmksrv'  # Replace with your server hostname or IP
    port = 656           # Default SSH port is 22
    username = 'user'     # Replace with your username
    password = '1'        # Replace with your password or None for key-based auth
    
    test_ssh_connection(hostname, port, username, password)
