from scapy.all import rdpcap, Ether, IP, TCP, UDP, ICMP
import csv
import os
import sys

def _get_label_for_timestamp(timestamp, label_timeline):
    for entry in label_timeline:
        if entry['start_time'] <= timestamp < entry['end_time']:
            return entry['label']
    return "unknown" # Default label if no match

def process_pcap_to_csv(pcap_file, output_csv_file, label_timeline=None):
    print(f"Processing {pcap_file} to {output_csv_file}...")
    
    if not os.path.exists(pcap_file):
        print(f"Error: PCAP file not found at {pcap_file}")
        return

    packets = rdpcap(pcap_file)

    with open(output_csv_file, 'w', newline='') as csvfile:
        fieldnames = [
            'timestamp', 'packet_length', 'eth_type',
            'ip_src', 'ip_dst', 'ip_proto', 'ip_ttl', 'ip_id', 'ip_flags', 'ip_len',
            'src_port', 'dst_port',
            'tcp_flags', 'tcp_seq', 'tcp_ack', 'tcp_window',
            'icmp_type', 'icmp_code', 'Label_multi', 'Label_binary'
        ]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for packet in packets:
            row = {
                'timestamp': packet.time,
                'packet_length': len(packet),
                'eth_type': '',
                'ip_src': '',
                'ip_dst': '',
                'ip_proto': '',
                'ip_ttl': '',
                'ip_id': '',
                'ip_flags': '',
                'ip_len': '',
                'src_port': '',
                'dst_port': '',
                'tcp_flags': '',
                'tcp_seq': '',
                'tcp_ack': '',
                'tcp_window': '',
                'icmp_type': '',
                'icmp_code': '',
                'Label_multi': 'unknown',
                'Label_binary': 0 # Default to 0 (normal)
            }

            if Ether in packet:
                row['eth_type'] = hex(packet[Ether].type)

            if IP in packet:
                row['ip_src'] = packet[IP].src
                row['ip_dst'] = packet[IP].dst
                row['ip_proto'] = packet[IP].proto
                row['ip_ttl'] = packet[IP].ttl
                row['ip_id'] = packet[IP].id
                row['ip_flags'] = str(packet[IP].flags)
                row['ip_len'] = packet[IP].len

                if TCP in packet:
                    row['src_port'] = packet[TCP].sport
                    row['dst_port'] = packet[TCP].dport
                    row['tcp_flags'] = str(packet[TCP].flags)
                    row['tcp_seq'] = packet[TCP].seq
                    row['tcp_ack'] = packet[TCP].ack
                    row['tcp_window'] = packet[TCP].window
                elif UDP in packet:
                    row['src_port'] = packet[UDP].sport
                    row['dst_port'] = packet[UDP].dport
                elif ICMP in packet:
                    row['icmp_type'] = packet[ICMP].type
                    row['icmp_code'] = packet[ICMP].code
            
            if label_timeline is not None:
                row['Label_multi'] = _get_label_for_timestamp(packet.time, label_timeline)
                if row['Label_multi'] != 'normal':
                    row['Label_binary'] = 1
            
            writer.writerow(row)
    
    print(f"Successfully processed {len(packets)} packets to {output_csv_file}")

if __name__ == "__main__":
    # This block is for standalone execution. When called from main.py, label_timeline is passed directly.
    # If run standalone, it will attempt to read label_timeline.csv if available.
    default_pcap_file = "traffic.pcap"
    default_output_csv_file = "packet_features.csv"
    default_label_timeline_file = "label_timeline.csv"

    pcap_file_arg = sys.argv[1] if len(sys.argv) > 1 else default_pcap_file
    output_csv_file_arg = sys.argv[2] if len(sys.argv) > 2 else default_output_csv_file

    timeline = None
    if os.path.exists(default_label_timeline_file):
        try:
            with open(default_label_timeline_file, 'r', newline='') as f:
                reader = csv.DictReader(f)
                timeline = []
                for row in reader:
                    timeline.append({
                        'start_time': float(row['start_time']),
                        'end_time': float(row['end_time']),
                        'label': row['label']
                    })
        except Exception as e:
            print(f"Warning: Could not read label_timeline.csv for standalone execution: {e}")

    process_pcap_to_csv(pcap_file_arg, output_csv_file_arg, timeline)
