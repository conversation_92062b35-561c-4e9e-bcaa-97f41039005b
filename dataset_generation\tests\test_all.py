#!/usr/bin/env python3
"""
One-Click Test Runner for AdDDoSDN Dataset Generation

This script runs all available tests with a single command and generates
comprehensive test reports.
"""
import os
import sys
import time
import subprocess
import logging
import platform
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional

# Configuration
TEST_DIR = Path(__file__).parent / "src" / "test"
SCRIPT_DIR = TEST_DIR / "scripts"
REPORT_DIR = Path("test_reports")
LOG_FILE = REPORT_DIR / f"test_run_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

# Ensure report directory exists
REPORT_DIR.mkdir(exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(LOG_FILE)
    ]
)
logger = logging.getLogger(__name__)

class TestRunner:
    def __init__(self):
        self.test_results = {}
        self.start_time = time.time()
        
    def run_test(self, test_name: str, test_script: str, args: Optional[List[str]] = None,
                cwd: Optional[Path] = None, is_script: bool = False) -> bool:
        """
        Run a single test script or command
        
        Args:
            test_name: Name of the test for reporting
            test_script: Path to the test script or command to run
            args: Additional arguments to pass to the test script
            cwd: Working directory for the test
            is_script: If True, treat as a script to execute directly
            
        Returns:
            bool: True if test passed, False otherwise
        """
        if args is None:
            args = []
            
        if cwd is None:
            cwd = Path.cwd()
            
        test_path = Path(test_script) if is_script else (SCRIPT_DIR if is_script else TEST_DIR) / test_script
        
        if not test_path.exists() and not is_script:
            logger.error(f"Test script not found: {test_path}")
            return False
            
        logger.info(f"🚀 Starting test: {test_name}")
        logger.debug(f"Test path: {test_path}")
        logger.debug(f"Working directory: {cwd}")
        logger.info(f"📜 Running: python {test_script} {' '.join(args)}")
        
        start_time = time.time()
        try:
            result = subprocess.run(
                [sys.executable, str(test_path)] + args,
                cwd=TEST_DIR,
                check=False,
                capture_output=True,
                text=True
            )
            
            # Log the results
            duration = time.time() - start_time
            success = result.returncode == 0
            
            if success:
                logger.info(f"✅ {test_name} completed successfully in {duration:.2f} seconds")
            else:
                logger.error(f"❌ {test_name} failed with exit code {result.returncode}")
                logger.error(f"Error output:\n{result.stderr}")
            
            # Save detailed results
            self.test_results[test_name] = {
                'success': success,
                'duration': duration,
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'command': f"python {test_script} {' '.join(args)}"
            }
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Error running {test_name}: {str(e)}")
            self.test_results[test_name] = {
                'success': False,
                'error': str(e)
            }
            return False
    
    def generate_report(self):
        """Generate a test report"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results.values() if r.get('success'))
        failed_tests = total_tests - passed_tests
        total_duration = time.time() - self.start_time
        
        report = [
            "=" * 80,
            " TEST EXECUTION REPORT",
            "=" * 80,
            f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Total tests: {total_tests}",
            f"Passed: {passed_tests}",
            f"Failed: {failed_tests}",
            f"Success rate: {passed_tests/max(total_tests, 1)*100:.1f}%",
            f"Total duration: {total_duration:.2f} seconds",
            "\n" + "-" * 40 + " DETAILED RESULTS " + "-" * 40,
        ]
        
        for test_name, result in self.test_results.items():
            status = "PASS" if result.get('success') else "FAIL"
            duration = f"{result.get('duration', 0):.2f}s" if 'duration' in result else "N/A"
            report.append(f"{status} | {test_name:<40} | {duration}")
            
            if not result.get('success'):
                if 'error' in result:
                    report.append(f"    Error: {result['error']}")
                if result.get('stderr'):
                    report.append("    Error output:")
                    for line in result['stderr'].split('\n'):
                        if line.strip():
                            report.append(f"      {line}")
        
        report_path = REPORT_DIR / f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_path, 'w') as f:
            f.write('\n'.join(report) + '\n')
        
        # Print summary to console
        logger.info("\n" + "=" * 80)
        logger.info(" TEST SUMMARY")
        logger.info("=" * 80)
        logger.info(f"Total tests run: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {failed_tests}")
        logger.info(f"Success rate: {passed_tests/max(total_tests, 1)*100:.1f}%")
        logger.info(f"Total duration: {total_duration:.2f} seconds")
        logger.info(f"\nDetailed report saved to: {report_path}")
        logger.info(f"Full log saved to: {LOG_FILE}")
        
        return report_path

def main():
    """Main function to run all tests"""
    logger.info("🚀 Starting AdDDoSDN Test Suite")
    logger.info(f"Test directory: {TEST_DIR.absolute()}")
    logger.info(f"Reports will be saved to: {REPORT_DIR.absolute()}")
    
    runner = TestRunner()
    
    # Define test sequence
    tests = [
        # Local tests
        {"name": "Simple Environment Check", "script": "simple_test.py"},
        
        # Unit tests
        {"name": "Run Unit Tests", "script": "pytest", "args": ["unit/"], "is_script": True},
        
        # Integration tests
        {"name": "Run Integration Tests", "script": "pytest", "args": ["integration/"], "is_script": True},
        
        # Remote tests (only on Windows with Posh-SSH)
        {
            "name": "Run Remote Tests", 
            "script": "powershell", 
            "args": ["-ExecutionPolicy", "Bypass", "-File", str(SCRIPT_DIR / "run_remote_tests.ps1")], 
            "is_script": True,
            "skip": platform.system() != 'Windows'
        },
        
        # End-to-end tests
        {"name": "Run End-to-End Tests", "script": "pytest", "args": ["e2e/"], "is_script": True}
    ]
    
    # Run all tests
    for test in tests:
        if test.get('skip', False):
            logger.info(f"⏩ Skipping test: {test['name']} (not supported on this platform)")
            continue
            
        success = runner.run_test(
            test["name"],
            test["script"],
            args=test.get('args', []),
            is_script=test.get('is_script', False)
        )
        
        if not success and test.get('critical', True):
            logger.error(f"❌ Critical test failed: {test['name']}")
            break
    
    # Generate and display report
    report_path = runner.generate_report()
    
    # Exit with appropriate status code
    if any(not result.get('success', False) for result in runner.test_results.values()):
        logger.error("\n❌ Some tests failed. Check the report for details.")
        sys.exit(1)
    else:
        logger.info("\n✅ All tests passed successfully!")
        sys.exit(0)

if __name__ == "__main__":
    main()
