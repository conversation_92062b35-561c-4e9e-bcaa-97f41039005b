# Parameters
$remoteUser = "user"
$remoteHost = "jtmksrv"
$remotePort = 656
$remotePassword = "1"
$remoteDir = "/home/<USER>/dataset/test_run"
$localDir = "."

# Create a temporary directory for the setup script
$tempDir = "$env:TEMP\sdn_test_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
New-Item -ItemType Directory -Path $tempDir -Force | Out-Null

# Create a setup script for the remote server
$setupScript = @"
#!/bin/bash
set -e

# Create directory structure
echo "Creating directory structure..."
mkdir -p $remoteDir
cd $remoteDir

# Install required packages
echo "Updating package lists..."
sudo apt-get update > /dev/null

echo "Installing required packages..."
sudo apt-get install -y python3 python3-pip python3-venv > /dev/null

# Create and activate virtual environment
echo "Setting up Python virtual environment..."
python3 -m venv venv
source venv/bin/activate

# Install Python dependencies
echo "Installing Python dependencies..."
pip install --upgrade pip > /dev/null
pip install -r requirements.txt > /dev/null

# Make scripts executable
chmod +x src/scripts/*.py

echo "Setup completed successfully!"
"@

# Save the setup script
$setupScript | Out-File -FilePath "$tempDir\setup_remote.sh" -Encoding ascii

# Copy required files to the temp directory
$excludeDirs = @(".git", "__pycache__", ".pytest_cache", "test_reports", "logs", "venv", ".vscode", ".idea", ".gitignore")
$excludeFiles = @("*.pyc", "*.pyo", "*.pyd", ".DS_Store", "*.log")

Get-ChildItem -Path $localDir -Recurse | Where-Object {
    $include = $true
    foreach ($dir in $excludeDirs) {
        if ($_.FullName -match [regex]::Escape($dir)) {
            $include = $false
            break
        }
    }
    if ($include -and -not $_.PSIsContainer) {
        foreach ($file in $excludeFiles) {
            if ($_.Name -like $file) {
                $include = $false
                break
            }
        }
    }
    return $include
} | Copy-Item -Destination {
    $destPath = $_.FullName.Replace($localDir, $tempDir)
    $destDir = [System.IO.Path]::GetDirectoryName($destPath)
    if (!(Test-Path $destDir)) {
        New-Item -ItemType Directory -Path $destDir -Force | Out-Null
    }
    $destPath
} -Force

# Create a zip of the files
$zipPath = "$tempDir\sdn_project.zip"
Compress-Archive -Path "$tempDir\*" -DestinationPath $zipPath -Force

# Transfer the zip file to the remote server
echo "Transferring files to remote server..."
$session = New-SSHSession -ComputerName $remoteHost -Port $remotePort -Credential (New-Object System.Management.Automation.PSCredential($remoteUser, (ConvertTo-SecureString $remotePassword -AsPlainText -Force)))

if ($session.Connected) {
    # Create remote directory
    $null = Invoke-SSHCommand -SSHSession $session -Command "mkdir -p $remoteDir"
    
    # Transfer the zip file
    $scp = New-SCPClient -ComputerName $remoteHost -Port $remotePort -Credential (New-Object System.Management.Automation.PSCredential($remoteUser, (ConvertTo-SecureString $remotePassword -AsPlainText -Force)))
    Set-SCPFile -ComputerName $remoteHost -Port $remotePort -Credential (New-Object System.Management.Automation.PSCredential($remoteUser, (ConvertTo-SecureString $remotePassword -AsPlainText -Force))) -LocalFile $zipPath -RemotePath "/tmp/"
    
    # Extract and setup on remote
    $commands = @(
        "cd $remoteDir",
        "unzip -q -o /tmp/sdn_project.zip -d .",
        "chmod +x setup_remote.sh",
        "nohup ./setup_remote.sh > setup.log 2>&1 &"
    )
    
    $null = Invoke-SSHCommand -SSHSession $session -Command ($commands -join " && ")
    
    Write-Host "Remote setup started. Check $remoteDir/setup.log for progress."
    
    # Cleanup
    Remove-SSHSession -SSHSession $session | Out-Null
} else {
    Write-Error "Failed to connect to remote server"
    exit 1
}

# Cleanup local temp files
Remove-Item -Path $tempDir -Recurse -Force -ErrorAction SilentlyContinue
