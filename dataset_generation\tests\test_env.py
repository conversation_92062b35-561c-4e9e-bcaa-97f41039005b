"""
Test script to verify Python environment and basic imports.
"""
import sys
import os
import platform
import importlib

def check_import(module_name):
    """Check if a module can be imported."""
    try:
        module = importlib.import_module(module_name)
        version = getattr(module, "__version__", "(no version info)")
        print(f"✓ {module_name} {version}")
        return True
    except ImportError as e:
        print(f"✗ {module_name}: {e}")
        return False

def main():
    print("\n=== Python Environment ===")
    print(f"Python: {platform.python_implementation()} {platform.python_version()}")
    print(f"System: {platform.system()} {platform.release()}")
    print(f"Working Dir: {os.getcwd()}")
    
    print("\n=== Checking Dependencies ===")
    required = [
        'pytest',
        'paramiko',
        'numpy',
        'pandas',
        'scapy',
        'ryu',
        'mininet',
        'matplotlib',
        'seaborn',
        'scikit-learn'
    ]
    
    results = [check_import(module) for module in required]
    
    print("\n=== Test Results ===")
    if all(results):
        print("All required packages are installed!")
    else:
        print("Some required packages are missing.")
    
    # Simple test function
    print("\nRunning a simple test function...")
    try:
        assert 1 + 1 == 2
        print("✓ Simple test passed!")
    except AssertionError:
        print("✗ Simple test failed!")

if __name__ == "__main__":
    main()
