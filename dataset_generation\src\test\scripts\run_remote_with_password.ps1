# Install SSH.NET if not already installed
if (-not (Get-Module -Name Posh-SSH -ListAvailable)) {
    Install-Module -Name Posh-SSH -Force -Scope CurrentUser
}

# Parameters
$remoteUser = "user"
$remoteHost = "jtmksrv"
$remotePort = 656
$remotePassword = ConvertTo-SecureString "1" -AsPlainText -Force
$remoteDir = "/home/<USER>/dataset/test_run"

# Create credential object
$credential = New-Object System.Management.Automation.PSCredential($remoteUser, $remotePassword)

try {
    # Create SSH session
    $session = New-SSHSession -ComputerName $remoteHost -Port $remotePort -Credential $credential -AcceptKey -Force
    
    if ($session.Connected) {
        Write-Host "Connected to $remoteHost"
        
        # Commands to run
        $commands = @(
            "cd $remoteDir",
            "source venv/bin/activate",
            "python -m pytest src/test/ -v"
        ) -join "; "
        
        # Execute commands
        $result = Invoke-SSHCommand -SSHSession $session -Command $commands
        
        # Display output
        $result.Output | ForEach-Object { Write-Host $_ }
        
        # Display errors if any
        if ($result.Error) {
            Write-Host "Errors:"
            $result.Error | ForEach-Object { Write-Host $_ -ForegroundColor Red }
        }
    } else {
        Write-Host "Failed to connect to $remoteHost"
    }
} catch {
    Write-Host "An error occurred: $_" -ForegroundColor Red
} finally {
    # Clean up the session
    if ($session -and $session.Connected) {
        Remove-SSHSession -SSHSession $session | Out-Null
    }
}
