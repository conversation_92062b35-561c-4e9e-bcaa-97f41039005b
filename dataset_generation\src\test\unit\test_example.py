"""
Example unit tests for the dataset generation project.

These tests demonstrate the structure and style for unit tests in the project.
"""
import pytest
from pathlib import Path

# Example function to test
def add_numbers(a: int, b: int) -> int:
    """Add two numbers together."""
    return a + b

# Example test class
class TestExample:
    """Test cases for the example functionality."""
    
    def test_add_positive_numbers(self):
        """Test adding two positive numbers."""
        assert add_numbers(2, 3) == 5
    
    def test_add_negative_numbers(self):
        """Test adding two negative numbers."""
        assert add_numbers(-1, -1) == -2
    
    @pytest.mark.parametrize("a,b,expected", [
        (0, 0, 0),
        (1, 0, 1),
        (0, 1, 1),
        (-1, 1, 0),
    ])
    def test_add_parameterized(self, a, b, expected):
        """Test adding numbers with different parameter combinations."""
        assert add_numbers(a, b) == expected
