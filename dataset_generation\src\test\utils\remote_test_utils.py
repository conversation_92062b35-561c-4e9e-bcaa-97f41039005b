"""
Utility functions for remote testing.
"""
import os
import logging
import paramiko
from pathlib import Path
from typing import Dict, Any, Optional, Tuple, List

logger = logging.getLogger(__name__)

class RemoteTestHelper:
    """Helper class for remote testing operations."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize with remote connection configuration."""
        self.config = config
        self.ssh = None
        self.sftp = None
    
    def connect(self) -> bool:
        """Establish SSH connection to the remote server."""
        try:
            self.ssh = paramiko.SSHClient()
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            connect_args = {
                'hostname': self.config['hostname'],
                'port': self.config.get('port', 22),
                'username': self.config['username'],
                'timeout': 10
            }
            
            if 'password' in self.config:
                connect_args['password'] = self.config['password']
            elif 'key_filename' in self.config:
                connect_args['key_filename'] = self.config['key_filename']
            
            self.ssh.connect(**connect_args)
            self.sftp = self.ssh.open_sftp()
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to remote server: {e}")
            self.close()
            return False
    
    def execute_command(self, command: str, timeout: int = 30) -> Tuple[int, str, str]:
        """Execute a command on the remote server."""
        if not self.ssh:
            raise RuntimeError("SSH connection not established")
        
        stdin, stdout, stderr = self.ssh.exec_command(command, timeout=timeout)
        exit_code = stdout.channel.recv_exit_status()
        return exit_code, stdout.read().decode().strip(), stderr.read().decode().strip()
    
    def upload_file(self, local_path: str, remote_path: str) -> bool:
        """Upload a file to the remote server."""
        if not self.sftp:
            raise RuntimeError("SFTP connection not established")
        
        try:
            self.sftp.put(local_path, remote_path)
            return True
        except Exception as e:
            logger.error(f"Failed to upload file: {e}")
            return False
    
    def download_file(self, remote_path: str, local_path: str) -> bool:
        """Download a file from the remote server."""
        if not self.sftp:
            raise RuntimeError("SFTP connection not established")
        
        try:
            self.sftp.get(remote_path, local_path)
            return True
        except Exception as e:
            logger.error(f"Failed to download file: {e}")
            return False
    
    def close(self) -> None:
        """Close all connections."""
        if self.sftp:
            self.sftp.close()
            self.sftp = None
        if self.ssh:
            self.ssh.close()
            self.ssh = None


def setup_remote_test_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """Set up default values for remote test configuration."""
    default_config = {
        'hostname': 'localhost',
        'port': 22,
        'username': None,
        'password': None,
        'key_filename': None,
        'remote_path': '/tmp/remote_test',
        'local_path': 'test_data',
        'timeout': 300
    }
    
    # Update defaults with provided config
    default_config.update(config)
    return default_config
