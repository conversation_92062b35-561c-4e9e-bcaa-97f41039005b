# 📚 AdDDoS-SDN Documentation

Welcome to the AdDDoS-SDN documentation! This directory contains detailed documentation for the AdDDoS-SDN Dataset Generation Framework.

## 📖 Documentation Structure

```
docs/
├── README.md           # This file - Documentation overview
├── progress.md         # Development progress and changelog
├── analysis.md         # Dataset analysis and statistics
├── scenario.md         # Attack scenarios and configurations
├── install.md          # Detailed installation guide
└── api/               # API documentation (if applicable)
```

## 🔍 Getting Started with Documentation

1. **New Users**
   - Start with the main [README.md](../README.md) for an overview
   - Check [install.md](install.md) for detailed setup instructions
   - Review [scenario.md](scenario.md) for attack scenarios

2. **Developers**
   - [progress.md](progress.md) - Development history and future plans
   - [analysis.md](analysis.md) - Dataset statistics and characteristics
   - Source code documentation in respective module directories

3. **Researchers**
   - [analysis.md](analysis.md) - Detailed dataset analysis
   - [scenario.md](scenario.md) - Attack scenarios and configurations
   - [progress.md](progress.md) - Evolution of the dataset

## 📄 Documentation Files

### [progress.md](progress.md)
- Development timeline
- Version history
- Known issues and limitations
- Future development plans

### [analysis.md](analysis.md)
- Dataset statistics
- Feature descriptions
- Data distribution
- Performance metrics

### [scenario.md](scenario.md)
- Attack scenarios
- Network topologies
- Traffic patterns
- Configuration examples

### [install.md](install.md)
- Detailed installation instructions
- Dependencies
- Configuration options
- Troubleshooting

## 📝 How to Contribute to Documentation

1. **Reporting Issues**
   - Use the issue tracker to report documentation problems
   - Suggest improvements or new documentation needs

2. **Updating Documentation**
   - Fork the repository
   - Make your changes following the existing style
   - Submit a pull request with a clear description

3. **Documentation Style**
   - Use clear, concise language
   - Include examples where helpful
   - Keep documentation up-to-date with code changes

## 🔗 Related Resources

- [Main Project Repository](https://github.com/nqmn/AdDDoSSDN-novel_adversarial_ddos_sdn_dataset)
- [Mininet Documentation](http://mininet.org/)
- [Ryu SDN Framework](https://ryu-sdn.org/)
- [Scapy Documentation](https://scapy.readthedocs.io/)

## 📜 License

This documentation is part of the AdDDoS-SDN project and is licensed under the MIT License.