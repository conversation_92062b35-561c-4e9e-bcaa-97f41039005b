{"mininet_topology": "topology.py", "ryu_app": "controller/ryu_controller_app.py", "controller_port": 6633, "api_port": 8080, "traffic_types": {"normal": {"duration": 10, "scapy_commands": [{"host": "h3", "command": "sendp(Ether()/IP(dst='********')/TCP(dport = 656, flags='S'), loop=1, inter=0.5, verbose=0)", "count": 5}]}, "attacks": [{"type": "syn_flood", "duration": 10, "attacker": "h1", "victim": "h6", "script_name": "gen_syn_flood.py"}]}, "offline_collection": {"pcap_file": "test_traffic.pcap", "output_file": "test_features.csv"}, "online_collection": {"output_file": "test_flow_features.csv", "poll_interval": 2}, "label_timeline_file": "test_timeline.csv"}