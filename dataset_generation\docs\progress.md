# Adversarial DDoS Attacks SDN Dataset - Progress

## Current Status (2025-07-08)
- Successfully connected to the remote server (jtmksrv:656)
- Uploaded all necessary files for testing
- Installed required dependencies (mininet, ryu, scapy, pandas)
- Currently running tests with detailed logging
- Reorganized test files into a dedicated `test` directory

## File Organization

```
dataset_generation/
├── attacks/                 # Attack generation scripts
├── controller/              # SDN controller code
├── test/                    # Test scripts and logs
│   ├── simple_test.py       # Basic environment check
│   ├── run_remote_test.py   # Remote test execution
│   ├── check_remote.py      # Remote server checker
│   ├── remote_exec.py       # Remote execution helper
│   ├── run_test_with_logging.py  # Detailed test runner
│   └── *.log                # Log files
├── config.json              # Main configuration
├── main.py                  # Main dataset generation script
├── progress.md              # This file
└── README.md                # Project documentation
```

## Recent Progress

### 1. Test Environment Setup
- Created a dedicated `test` directory for better organization
- Moved all test-related scripts and logs to the `test` directory
- Fixed path handling issues for cross-platform compatibility

### 2. Remote Testing Improvements
- Implemented robust SSH connection handling
- Added detailed logging for remote command execution
- Created helper scripts for checking remote server status
- Fixed file upload/download functionality

### 3. Test Execution
- Running with detailed logging enabled
- Monitoring progress through log files
- Expected output files:
  - `test_traffic.pcap`
  - `test_features.csv`
  - `test_flow_features.csv`
  - `test_timeline.csv`

## Known Issues
1. Some log files couldn't be moved due to being in use
2. Need to verify all dependencies are correctly installed on the remote server
3. May need to adjust timeouts for long-running tests

## Next Steps
1. Monitor the remote test execution
2. Download and analyze the generated dataset files
3. Document any issues encountered and their solutions
4. Update the README with setup and usage instructions

## Detailed Implementation and Debugging Log:

### 1. Initial Request and Environment Setup
*   **User Request:** Run the application on a remote server using SSH.
*   **Initial Action:** Read `ssh_connect.py` and `main.py` to understand the existing setup.
*   **Information Needed:** Remote server hostname/IP, SSH port, username, password, and the remote path for the `dataset_generation` directory.
*   **User Provided:** Remote path `/home/<USER>/dataset`.

### 2. Directory Transfer and Initial Execution Attempts
*   **Action:** Attempted to copy the `dataset_generation` directory to the remote server using `scp`.
*   **Issue:** `ssh_connect.py`'s `copy_file` function was designed for single files, not directories. Used `scp` via `run_shell_command` instead.
*   **User Confirmation:** Confirmed `dataset_generation` was on `/home/<USER>

### 3. Dependency Installation Challenges
*   **Action:** Attempted to install Python dependencies (`requirements.txt`) on the remote server.
*   **Issue:** `pip` command not found, then `python3 -m pip` also failed with "command not found".
*   **Diagnosis:** Suspected incorrect command execution due to extraneous quotes in `ssh_connect.py`.
*   **Debugging:** Tested with `ls -l` and `echo hello`, both failed with "command not found", confirming the quote issue.
*   **Resolution:** Modified `ssh_connect.py` to strip extraneous quotes from commands.
*   **Re-attempt:** Retried `python3 -m pip install -r requirements.txt`.
*   **New Issue:** `ERROR: Could not open requirements file: [Errno 2] No such file or directory: 'requirements.txt --dir /home/<USER>/dataset_generation'`. This indicated the `--dir` argument was being passed to `pip` instead of `ssh_connect.py`.
*   **Resolution:** Further modified `ssh_connect.py` to correctly parse the `--dir` argument and separate it from the command to be executed.
*   **Re-attempt:** Successfully installed dependencies.

### 4. Application Execution and Sudo Privileges
*   **User Confirmation:** `sudo mn` and `ryu-manager` are confirmed to be installed on the remote server.
*   **Action:** Attempted to run `main.py` in the background using `nohup`.
*   **Issue:** `main.py` exited immediately. `output.log` showed "This script requires sudo privileges. Please run with sudo."
*   **Action:** Attempted to run `main.py` with `sudo`.
*   **Issue:** `sudo: no tty present and no askpass program specified`. This is a common issue when running `sudo` non-interactively without proper `sudoers` configuration.
*   **User Instruction:** Advised user to manually configure `sudoers` or run the command directly via SSH.
*   **User Confirmation:** User indicated they had "done" this.

### 5. File Path and Mininet/Ryu Connection Issues (Post-Sudo)
*   **Action:** Checked `main.py` status and `output.log`.
*   **Issue:** `main.py` was still not running. `output.log` showed `FileNotFoundError: '/home/<USER>/dataset_generation/dataset_generation/topology.py'`.
*   **Diagnosis:** Incorrect path in `config.json`.
*   **Resolution:** Corrected `mininet_topology` and `ryu_app` paths in `config.json` on the remote server using `sed`.
*   **Action:** Re-attempted to run `main.py` with `sudo` (via the `restart_remote_app.py` script).
*   **Issue:** Still encountered `sudo: no tty present` error.
*   **User Instruction:** Re-iterated the need for manual execution of `sudo` commands on the remote server.
*   **User Confirmation:** User indicated they had "done" this.
*   **Action:** Checked `main.py` status and `output.log`.
*   **Issue:** `main.py` was running, but `output.log` showed "Unable to contact the remote controller at 127.0.0.1:6633" and `AttributeError: 'str' object has no attribute 'send_signal'`. `ryu_controller.log` was not found.
*   **Diagnosis:** Ryu controller not starting correctly or Mininet connecting too soon. `host.cmd()` returns a string, not a process object, causing the `AttributeError`.
*   **Resolution (Attempted):** Increased `time.sleep` in `main.py` from 5 to 10 seconds. Modified `main.py` to use `host.popen()` and redirect Ryu's output to `ryu_controller.log`.
*   **User Instruction:** Advised user to manually stop processes and restart `main.py`.
*   **User Confirmation:** User indicated they had "done" this.
*   **Action:** Checked `main.py` status and `output.log`.
*   **Issue:** `main.py` was running, but the Ryu connection issue persisted, and the `AttributeError` was still present. `ryu_controller.log` was still not found.
*   **Diagnosis:** The `main.py` changes were not consistently applied to the remote server.

### 6. Ryu Library and `main.py` Synchronization
*   **Action:** Attempted to run `ryu-manager` directly to diagnose.
*   **Issue:** `ImportError: cannot import name 'ControllerWSGI'`.
*   **Resolution:** Uninstalled and reinstalled `eventlet` (a dependency of Ryu).
*   **Action:** Re-attempted to run `ryu-manager` directly.
*   **Issue:** `ImportError: cannot import name 'ALREADY_HANDLED'` from `eventlet.wsgi`.
*   **Action:** Re-applied `host.popen()` fix to `main.py` and ensured Ryu's output redirection.
*   **User Instruction:** Advised user to manually stop processes and restart `main.py`.
*   **User Request:** User asked me to run the `restart_remote_app.py` script.
*   **Action:** Attempted to run `restart_remote_app.py`.
*   **Issue:** `restart_remote_app.py` was not found locally.
*   **Resolution:** Created `restart_remote_app.py` locally.
*   **Action:** Ran `restart_remote_app.py`.
*   **Action:** Checked `main.py` status and `output.log`.
*   **Issue:** `main.py` was not running, and `output.log` still showed the `AttributeError` and `ryu_controller.log` was not found. This indicates the `main.py` changes are still not consistently applied to the remote server.

### 7. Incorporating Adversarial Attacks and Dataset Balancing (Planned - *Now Implemented*)
*   **Goal:** Modify `config.json` and `main.py` to include and differentiate between basic and adversarial attacks, and to balance the dataset.
*   **Planned `config.json` changes:**
    *   Rename existing basic attack types (e.g., `syn_flood` to `basic_syn_flood`).
    *   Add new entries for adversarial attacks, referencing `gen_advanced_adversarial_ddos_attacks.py`.
    *   Include an `attack_function` field to specify which function within `gen_advanced_adversarial_ddos_attacks.py` should be called (e.g., `multi_vector_attack`, `slow_read_attack`, `tcp_state_exhaustion`).
    *   Add an `attack_args` dictionary for adversarial attacks to pass specific arguments to their functions.
    *   **Crucially, increase `normal.duration` from 60 seconds to 1800 seconds (30 minutes) to balance the normal traffic duration with the total attack duration (1800 seconds).** This aims to achieve a more balanced number of online flow samples between normal and attack periods for a single run.
*   **Planned `main.py` changes:**
    *   Update the `_generate_traffic` method to dynamically call the appropriate attack function based on the `attack_function` and `attack_args` fields in `config.json`.

## Next Steps:

1.  **Remote Server Availability:** Wait for the remote server to be back online.
2.  **Force Copy `main.py`:** Once the server is available, I will force copy the local, corrected `main.py` to the remote server to ensure all fixes are applied.
3.  **Update `config.json`:** Apply the planned changes to `config.json` on the remote server to include adversarial attacks and the updated normal traffic duration.
4.  **Diagnose Ryu Controller Startup:** Re-run the application and examine `ryu_controller.log` for specific errors related to Ryu's startup.
5.  **Address any new errors:** Based on the new logs, we will continue to debug and resolve any further issues.
