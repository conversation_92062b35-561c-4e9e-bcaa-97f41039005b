# Testing Guide for AdDDoSDN Dataset Generation

This document provides comprehensive information about testing the AdDDoSDN Dataset Generation framework.

## Test Suite Overview

The framework includes several test scripts that can be run individually or using the one-click test runner:

- `simple_test.py`: Basic environment and configuration checks
- `check_remote.py`: Verifies remote server connectivity and file access
- `run_remote_test.py`: Runs basic remote tests
- `run_remote_test_detailed.py`: Runs comprehensive remote tests
- `run_test_with_logging.py`: Runs tests with detailed logging
- `test_all.py`: One-click test runner (recommended)

## Running Tests

### Prerequisites

1. Python 3.6 or higher
2. Required Python packages (install using `pip install -r requirements.txt`)
3. Access to the remote test server
4. Proper SSH configuration for the remote server

### One-Click Test Runner (Recommended)

Run all tests with a single command:

```bash
python test_all.py
```

This will:
1. Run all available tests in sequence
2. Display real-time progress and results
3. Generate a detailed test report
4. Save logs to `test_reports/` directory

### Individual Tests

You can also run individual test scripts:

```bash
# Run simple environment check
python src/test/simple_test.py

# Check remote server connectivity
python src/test/check_remote.py

# Run basic remote test
python src/test/run_remote_test.py

# Run detailed remote test
python src/test/run_remote_test_detailed.py

# Run test with detailed logging
python src/test/run_test_with_logging.py
```

## Test Reports

Test reports are automatically generated in the `test_reports/` directory with timestamps in the filenames.

Example report structure:
```
test_reports/
├── test_run_20230708_153000.log
├── test_report_20230708_153000.txt
└── ...
```

## Test Configuration

Test configuration is managed in the individual test scripts. The default configuration uses:
- Hostname: `jtmksrv`
- Port: `656`
- Username: `user`
- Remote Path: `/home/<USER>/dataset/test_run`

To modify these settings, edit the respective test scripts or use the `update_test_configs.py` script:

```bash
python src/test/update_test_configs.py
```

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Verify the remote server is running and accessible
   - Check firewall settings and port accessibility

2. **Authentication Failed**
   - Verify username and password
   - Ensure SSH key is properly configured if using key-based auth

3. **Missing Dependencies**
   - Run `pip install -r requirements.txt`
   - Ensure all system packages are installed (Mininet, Open vSwitch, etc.)

4. **Permission Denied**
   - Ensure the test user has proper permissions on the remote server
   - Check file and directory permissions in the remote path

## Adding New Tests

1. Create a new Python script in the `src/test/` directory
2. Follow the existing test patterns
3. Update `test_all.py` to include the new test
4. Document the test in this file

## Continuous Integration

The test suite can be integrated into CI/CD pipelines. See `.github/workflows/` for example workflows.

## Support

For issues with the test suite, please open an issue on the project repository.
