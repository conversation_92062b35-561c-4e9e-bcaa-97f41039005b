2025-07-08 22:05:33,238 - INFO - Test directory: C:\Users\<USER>\Desktop\InSDN_ddos_dataset\adversarial-ddos-attacks-sdn-dataset\dataset_generation\src\test
2025-07-08 22:05:33,238 - INFO - Reports will be saved to: C:\Users\<USER>\Desktop\InSDN_ddos_dataset\adversarial-ddos-attacks-sdn-dataset\dataset_generation\test_reports
2025-07-08 22:05:33,323 - ERROR - Error output:
2025-07-08 22:05:33,313 - INFO - Starting simple test...
2025-07-08 22:05:33,313 - INFO - Checking environment...
2025-07-08 22:05:33,313 - ERROR - Required directory not found: attacks
2025-07-08 22:05:33,313 - ERROR - Environment check failed. Please fix the issues and try again.

2025-07-08 22:05:36,529 - ERROR - Error output:
2025-07-08 22:05:35,301 - INFO - Starting remote test execution...
2025-07-08 22:05:35,302 - INFO - Connecting to user@jtmksrv:656
2025-07-08 22:05:35,352 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-08 22:05:35,536 - INFO - Authentication (password) successful!
2025-07-08 22:05:35,723 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-08 22:05:35,723 - INFO - SSH connection established
2025-07-08 22:05:35,723 - INFO - Executing: mkdir -p \home\user\dataset\test_run
2025-07-08 22:05:35,831 - INFO - Executing: mkdir -p /home/<USER>/dataset/test_run/controller
2025-07-08 22:05:36,014 - INFO - Executing: mkdir -p /home/<USER>/dataset/test_run/attacks
2025-07-08 22:05:36,182 - INFO - Uploading simple_test.py to /home/<USER>/dataset/test_run/simple_test.py
2025-07-08 22:05:36,369 - INFO - Uploaded simple_test.py to /home/<USER>/dataset/test_run/simple_test.py
2025-07-08 22:05:36,369 - WARNING - Local file not found: main.py
2025-07-08 22:05:36,369 - WARNING - Local file not found: config.json
2025-07-08 22:05:36,369 - WARNING - Local file not found: topology.py
2025-07-08 22:05:36,370 - WARNING - Local file not found: controller\ryu_controller_app.py
2025-07-08 22:05:36,370 - WARNING - Local file not found: attacks\gen_syn_flood.py
2025-07-08 22:05:36,370 - WARNING - Local file not found: attacks\gen_udp_flood.py
2025-07-08 22:05:36,370 - WARNING - Local file not found: attacks\gen_advanced_adversarial_ddos_attacks.py
2025-07-08 22:05:36,370 - INFO - Running environment check...
2025-07-08 22:05:36,370 - INFO - Executing: cd /home/<USER>/dataset/test_run && python3 simple_test.py
2025-07-08 22:05:36,479 - INFO - Output: 2025-07-08 22:05:37,121 - INFO - Starting simple test...

2025-07-08 22:05:37,121 - INFO - Checking environment...

2025-07-08 22:05:37,121 - ERROR - Required file not found: main.py

2025-07-08 22:05:37,121 - ERROR - Environment check failed. Please fix the issues and try again.
2025-07-08 22:05:36,481 - ERROR - Environment check failed
2025-07-08 22:05:36,482 - ERROR - \u274c Remote test failed
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 34: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Desktop\InSDN_ddos_dataset\adversarial-ddos-attacks-sdn-dataset\dataset_generation\src\test\run_remote_test.py", line 229, in <module>
    sys.exit(main())
  File "C:\Users\<USER>\Desktop\InSDN_ddos_dataset\adversarial-ddos-attacks-sdn-dataset\dataset_generation\src\test\run_remote_test.py", line 225, in main
    logger.error("\u274c Remote test failed")
Message: '\u274c Remote test failed'
Arguments: ()

