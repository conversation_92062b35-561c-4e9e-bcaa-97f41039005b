"""
A simple test script to verify Python environment and basic functionality.
"""

def test_addition():
    """Test basic addition."""
    assert 1 + 1 == 2, "1 + 1 should equal 2"

def test_imports():
    """Test importing required packages."""
    try:
        import pytest
        import paramiko
        import numpy
        import pandas
        import scapy
        import ryu
        import mininet
        import matplotlib
        import seaborn
        import sklearn
        print("All required packages are installed!")
        return True
    except ImportError as e:
        print(f"Missing package: {e}")
        return False

if __name__ == "__main__":
    print("Running simple tests...")
    test_addition()
    print("✓ Basic addition test passed!")
    
    print("\nTesting package imports...")
    if test_imports():
        print("✓ All imports successful!")
    else:
        print("✗ Some imports failed.")
    
    print("\nTest completed!")
