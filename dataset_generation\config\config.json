{"mininet_topology": "topology.py", "ryu_app": "controller/ryu_controller_app.py", "controller_port": 6633, "api_port": 8080, "traffic_types": {"normal": {"duration": 1800, "scapy_commands": [{"host": "h3", "command": "sendp(Ether()/IP(dst='********')/TCP(dport=80, flags='S'), loop=1, inter=0.1)"}, {"host": "h5", "command": "sendp(Ether()/IP(dst='********')/UDP(dport=53), loop=1, inter=0.1)"}]}, "attacks": [{"type": "syn_flood", "duration": 30, "attacker": "h1", "victim": "h6", "script_name": "gen_syn_flood.py"}, {"type": "syn_flood", "duration": 30, "attacker": "h2", "victim": "h6", "script_name": "gen_syn_flood.py"}, {"type": "udp_flood", "duration": 30, "attacker": "h2", "victim": "h4", "script_name": "gen_udp_flood.py"}, {"type": "icmp_flood", "duration": 30, "attacker": "h2", "victim": "h4", "script_name": "gen_icmp_flood.py"}, {"type": "ad_syn_flood", "duration": 30, "attacker": "h1", "victim": "h6", "script_name": "gen_advanced_adversarial_ddos_attacks.py", "attack_variant": "tcp_state_exhaustion"}, {"type": "ad_udp_flood", "duration": 30, "attacker": "h2", "victim": "h4", "script_name": "gen_advanced_adversarial_ddos_attacks.py", "attack_variant": "application_layer"}, {"type": "ad_icmp_flood", "duration": 30, "attacker": "h2", "victim": "h4", "script_name": "gen_advanced_adversarial_ddos_attacks.py", "attack_variant": "slow_read"}, {"type": "ad_multi_vector", "duration": 60, "attacker": "h1", "victim": "h6", "script_name": "gen_advanced_adversarial_ddos_attacks.py", "attack_variant": "multi_vector"}]}, "offline_collection": {"pcap_file": "traffic.pcap", "output_file": "packet_features.csv"}, "online_collection": {"output_file": "ryu_flow_features.csv", "poll_interval": 2}, "label_timeline_file": "label_timeline.csv"}