2025-07-09 02:48:37,655 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 02:48:37,656 - DEBUG - Using password authentication
2025-07-09 02:48:37,743 - DEBUG - starting thread (client mode): 0x329fcec0
2025-07-09 02:48:37,743 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 02:48:37,770 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 02:48:37,770 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 02:48:37,792 - DEBUG - === Key exchange possibilities ===
2025-07-09 02:48:37,793 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 02:48:37,794 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 02:48:37,795 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:48:37,795 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:48:37,796 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:48:37,796 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:48:37,797 - DEBUG - client compress: none, <EMAIL>
2025-07-09 02:48:37,797 - DEBUG - server compress: none, <EMAIL>
2025-07-09 02:48:37,797 - DEBUG - client lang: <none>
2025-07-09 02:48:37,798 - DEBUG - server lang: <none>
2025-07-09 02:48:37,798 - DEBUG - kex follows: False
2025-07-09 02:48:37,798 - DEBUG - === Key exchange agreements ===
2025-07-09 02:48:37,799 - DEBUG - Kex: <EMAIL>
2025-07-09 02:48:37,799 - DEBUG - HostKey: ssh-ed25519
2025-07-09 02:48:37,800 - DEBUG - Cipher: aes128-ctr
2025-07-09 02:48:37,800 - DEBUG - MAC: hmac-sha2-256
2025-07-09 02:48:37,801 - DEBUG - Compression: none
2025-07-09 02:48:37,801 - DEBUG - === End of kex handshake ===
2025-07-09 02:48:37,832 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 02:48:37,832 - DEBUG - Switch to new keys ...
2025-07-09 02:48:37,833 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 02:48:37,833 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 02:48:37,915 - DEBUG - userauth is OK
2025-07-09 02:48:37,937 - INFO - Authentication (password) successful!
2025-07-09 02:48:37,937 - INFO - SSH connection established successfully
2025-07-09 02:48:37,938 - DEBUG - Executing command: cd /path/to/project/directory && pwd
2025-07-09 02:48:37,938 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 02:48:38,032 - DEBUG - Received global request "<EMAIL>"
2025-07-09 02:48:38,033 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 02:48:38,131 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 02:48:38,131 - DEBUG - Secsh channel 0 opened.
2025-07-09 02:48:38,151 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 02:48:38,224 - DEBUG - [chan 0] EOF received (0)
2025-07-09 02:48:38,224 - ERROR - Command failed with exit code 1: cd /path/to/project/directory && pwd
2025-07-09 02:48:38,224 - ERROR - STDERR: bash: line 0: cd: /path/to/project/directory: No such file or directory
2025-07-09 02:48:38,224 - DEBUG - [chan 0] EOF sent (0)
2025-07-09 02:48:38,225 - DEBUG - Executing command: python main.py
2025-07-09 02:48:38,225 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 02:48:38,309 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 02:48:38,309 - DEBUG - Secsh channel 1 opened.
2025-07-09 02:48:38,330 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 02:48:38,349 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 02:48:38,424 - DEBUG - [chan 1] EOF received (1)
2025-07-09 02:48:38,425 - ERROR - Command failed with exit code 2: python main.py
2025-07-09 02:48:38,426 - ERROR - STDERR: 
2025-07-09 02:48:38,426 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 02:48:38,428 - DEBUG - EOF in transport thread
2025-07-09 02:49:06,108 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 02:49:06,109 - DEBUG - Using password authentication
2025-07-09 02:49:06,186 - DEBUG - starting thread (client mode): 0xad12a330
2025-07-09 02:49:06,186 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 02:49:06,189 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 02:49:06,189 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 02:49:06,254 - DEBUG - === Key exchange possibilities ===
2025-07-09 02:49:06,255 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 02:49:06,255 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 02:49:06,256 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:49:06,256 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:49:06,257 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:49:06,257 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:49:06,258 - DEBUG - client compress: none, <EMAIL>
2025-07-09 02:49:06,258 - DEBUG - server compress: none, <EMAIL>
2025-07-09 02:49:06,258 - DEBUG - client lang: <none>
2025-07-09 02:49:06,259 - DEBUG - server lang: <none>
2025-07-09 02:49:06,259 - DEBUG - kex follows: False
2025-07-09 02:49:06,259 - DEBUG - === Key exchange agreements ===
2025-07-09 02:49:06,260 - DEBUG - Kex: <EMAIL>
2025-07-09 02:49:06,260 - DEBUG - HostKey: ssh-ed25519
2025-07-09 02:49:06,260 - DEBUG - Cipher: aes128-ctr
2025-07-09 02:49:06,260 - DEBUG - MAC: hmac-sha2-256
2025-07-09 02:49:06,260 - DEBUG - Compression: none
2025-07-09 02:49:06,260 - DEBUG - === End of kex handshake ===
2025-07-09 02:49:06,338 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 02:49:06,340 - DEBUG - Switch to new keys ...
2025-07-09 02:49:06,341 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 02:49:06,342 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 02:49:06,426 - DEBUG - userauth is OK
2025-07-09 02:49:06,447 - INFO - Authentication (password) successful!
2025-07-09 02:49:06,448 - INFO - SSH connection established successfully
2025-07-09 02:49:06,448 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 02:49:06,448 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 02:49:06,545 - DEBUG - Received global request "<EMAIL>"
2025-07-09 02:49:06,545 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 02:49:06,614 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 02:49:06,614 - DEBUG - Secsh channel 0 opened.
2025-07-09 02:49:06,675 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 02:49:06,742 - DEBUG - [chan 0] EOF received (0)
2025-07-09 02:49:06,742 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 02:49:06,743 - DEBUG - [chan 0] EOF sent (0)
2025-07-09 02:49:06,743 - DEBUG - STDOUT: 
2025-07-09 02:49:06,743 - DEBUG - Executing command: cd ~/dataset_generation && pwd
2025-07-09 02:49:06,743 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 02:49:06,824 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 02:49:06,824 - DEBUG - Secsh channel 1 opened.
2025-07-09 02:49:06,846 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 02:49:06,917 - DEBUG - [chan 1] EOF received (1)
2025-07-09 02:49:06,917 - INFO - Command executed successfully: cd ~/dataset_generation && pwd
2025-07-09 02:49:06,918 - DEBUG - STDOUT: /home/<USER>/dataset_generation
2025-07-09 02:49:06,919 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 02:49:06,920 - DEBUG - Executing command: python main.py
2025-07-09 02:49:06,921 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 02:49:07,011 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 02:49:07,011 - DEBUG - Secsh channel 2 opened.
2025-07-09 02:49:07,031 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 02:49:07,059 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 02:49:07,134 - DEBUG - [chan 2] EOF received (2)
2025-07-09 02:49:07,135 - ERROR - Command failed with exit code 2: python main.py
2025-07-09 02:49:07,135 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 02:49:07,135 - ERROR - STDERR: 
2025-07-09 02:49:07,136 - DEBUG - Dropping user packet because connection is dead.
2025-07-09 02:50:36,273 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 02:50:36,273 - DEBUG - Using password authentication
2025-07-09 02:50:36,338 - DEBUG - starting thread (client mode): 0x68fa0680
2025-07-09 02:50:36,338 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 02:50:36,362 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 02:50:36,362 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 02:50:36,390 - DEBUG - === Key exchange possibilities ===
2025-07-09 02:50:36,391 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 02:50:36,391 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 02:50:36,392 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:50:36,392 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:50:36,392 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:50:36,393 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:50:36,393 - DEBUG - client compress: none, <EMAIL>
2025-07-09 02:50:36,394 - DEBUG - server compress: none, <EMAIL>
2025-07-09 02:50:36,394 - DEBUG - client lang: <none>
2025-07-09 02:50:36,394 - DEBUG - server lang: <none>
2025-07-09 02:50:36,394 - DEBUG - kex follows: False
2025-07-09 02:50:36,394 - DEBUG - === Key exchange agreements ===
2025-07-09 02:50:36,394 - DEBUG - Kex: <EMAIL>
2025-07-09 02:50:36,395 - DEBUG - HostKey: ssh-ed25519
2025-07-09 02:50:36,395 - DEBUG - Cipher: aes128-ctr
2025-07-09 02:50:36,395 - DEBUG - MAC: hmac-sha2-256
2025-07-09 02:50:36,395 - DEBUG - Compression: none
2025-07-09 02:50:36,395 - DEBUG - === End of kex handshake ===
2025-07-09 02:50:36,464 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 02:50:36,466 - DEBUG - Switch to new keys ...
2025-07-09 02:50:36,467 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 02:50:36,468 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 02:50:36,557 - DEBUG - userauth is OK
2025-07-09 02:50:36,578 - INFO - Authentication (password) successful!
2025-07-09 02:50:36,579 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 02:50:36,671 - DEBUG - Received global request "<EMAIL>"
2025-07-09 02:50:36,672 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 02:50:36,743 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 02:50:36,743 - DEBUG - Secsh channel 0 opened.
2025-07-09 02:50:36,762 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 02:50:36,782 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 02:50:36,783 - INFO - SSH connection established successfully
2025-07-09 02:50:37,140 - DEBUG - [chan 0] open(b'~/dataset_generation/project.zip', 'wb')
2025-07-09 02:50:37,160 - DEBUG - Executing command: unzip -o ~/dataset_generation/project.zip -d ~/dataset_generation
2025-07-09 02:50:37,160 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 02:50:37,180 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 02:50:37,180 - DEBUG - Secsh channel 1 opened.
2025-07-09 02:50:37,200 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 02:50:37,276 - DEBUG - [chan 1] EOF received (1)
2025-07-09 02:50:37,277 - ERROR - Command failed with exit code 9: unzip -o ~/dataset_generation/project.zip -d ~/dataset_generation
2025-07-09 02:50:37,277 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 02:50:37,277 - ERROR - STDERR: unzip:  cannot find or open /home/<USER>/dataset_generation/project.zip, /home/<USER>/dataset_generation/project.zip.zip or /home/<USER>/dataset_generation/project.zip.ZIP.
2025-07-09 02:50:37,277 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 02:50:37,277 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 02:50:37,363 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 02:50:37,364 - DEBUG - Secsh channel 2 opened.
2025-07-09 02:50:37,385 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 02:50:37,448 - DEBUG - [chan 2] EOF received (2)
2025-07-09 02:50:37,449 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 02:50:37,449 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 02:50:37,449 - DEBUG - STDOUT: 
2025-07-09 02:50:37,449 - DEBUG - Executing command: cd ~/dataset_generation && pwd
2025-07-09 02:50:37,449 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 02:50:37,537 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 02:50:37,537 - DEBUG - Secsh channel 3 opened.
2025-07-09 02:50:37,563 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 02:50:37,637 - DEBUG - [chan 3] EOF received (3)
2025-07-09 02:50:37,637 - INFO - Command executed successfully: cd ~/dataset_generation && pwd
2025-07-09 02:50:37,638 - DEBUG - STDOUT: /home/<USER>/dataset_generation
2025-07-09 02:50:37,638 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 02:50:37,639 - DEBUG - Executing command: python main.py
2025-07-09 02:50:37,639 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 02:50:37,725 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 02:50:37,725 - DEBUG - Secsh channel 4 opened.
2025-07-09 02:50:37,748 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 02:50:37,771 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 02:50:37,844 - DEBUG - [chan 4] EOF received (4)
2025-07-09 02:50:37,844 - ERROR - Command failed with exit code 2: python main.py
2025-07-09 02:50:37,845 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 02:50:37,845 - ERROR - STDERR: 
2025-07-09 02:50:37,846 - INFO - [chan 0] sftp session closed.
2025-07-09 02:50:37,846 - DEBUG - [chan 0] EOF sent (0)
2025-07-09 02:50:37,847 - DEBUG - EOF in transport thread
2025-07-09 02:50:55,526 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 02:50:55,526 - DEBUG - Using password authentication
2025-07-09 02:50:55,599 - DEBUG - starting thread (client mode): 0x1e6bfa40
2025-07-09 02:50:55,599 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 02:50:55,600 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 02:50:55,600 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 02:50:55,665 - DEBUG - === Key exchange possibilities ===
2025-07-09 02:50:55,665 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 02:50:55,665 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 02:50:55,665 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:50:55,665 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:50:55,665 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:50:55,665 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:50:55,666 - DEBUG - client compress: none, <EMAIL>
2025-07-09 02:50:55,666 - DEBUG - server compress: none, <EMAIL>
2025-07-09 02:50:55,666 - DEBUG - client lang: <none>
2025-07-09 02:50:55,666 - DEBUG - server lang: <none>
2025-07-09 02:50:55,666 - DEBUG - kex follows: False
2025-07-09 02:50:55,666 - DEBUG - === Key exchange agreements ===
2025-07-09 02:50:55,666 - DEBUG - Kex: <EMAIL>
2025-07-09 02:50:55,666 - DEBUG - HostKey: ssh-ed25519
2025-07-09 02:50:55,666 - DEBUG - Cipher: aes128-ctr
2025-07-09 02:50:55,666 - DEBUG - MAC: hmac-sha2-256
2025-07-09 02:50:55,666 - DEBUG - Compression: none
2025-07-09 02:50:55,666 - DEBUG - === End of kex handshake ===
2025-07-09 02:50:55,753 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 02:50:55,755 - DEBUG - Switch to new keys ...
2025-07-09 02:50:55,756 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 02:50:55,756 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 02:50:55,842 - DEBUG - userauth is OK
2025-07-09 02:50:55,866 - INFO - Authentication (password) successful!
2025-07-09 02:50:55,867 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 02:50:55,962 - DEBUG - Received global request "<EMAIL>"
2025-07-09 02:50:55,962 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 02:50:56,030 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 02:50:56,030 - DEBUG - Secsh channel 0 opened.
2025-07-09 02:50:56,051 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 02:50:56,072 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 02:50:56,072 - INFO - SSH connection established successfully
2025-07-09 02:50:56,073 - DEBUG - Executing command: echo $HOME
2025-07-09 02:50:56,073 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 02:50:56,100 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 02:50:56,100 - DEBUG - Secsh channel 1 opened.
2025-07-09 02:50:56,119 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 02:50:56,188 - DEBUG - [chan 1] EOF received (1)
2025-07-09 02:50:56,189 - INFO - Command executed successfully: echo $HOME
2025-07-09 02:50:56,190 - DEBUG - STDOUT: /home/<USER>
2025-07-09 02:50:56,190 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 02:50:56,310 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 02:50:56,357 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 02:50:56,629 - DEBUG - [chan 0] close(00000000)
2025-07-09 02:50:56,688 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 02:50:56,713 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:50:56,713 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 02:50:56,733 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 02:50:56,733 - DEBUG - Secsh channel 2 opened.
2025-07-09 02:50:56,753 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 02:50:56,857 - DEBUG - [chan 2] EOF received (2)
2025-07-09 02:50:56,858 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:50:56,858 - DEBUG - STDOUT: Archive:  /home/<USER>/project.zip
  inflating: /home/<USER>/dataset_generation/dataset_generation/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/project.zip  
  inflating: /home/<USER>/dataset_generation/dataset_generation/pytest.ini  
  inflating: /home/<USER>/dataset_generation/dataset_generation/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_remote_generation.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/setup.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test-requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/TESTING.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_debug.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/.gitignore  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/CACHEDIR.TAG  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/lastfailed  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/nodeids  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/analysis.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/install.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/progress.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/scenario.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/summary.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/app.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220519.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220937.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/ssh_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/topology.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen-attack-script.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_advanced_adversarial_ddos_attacks.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_icmp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_syn_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_udp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow-generic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_icmp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_syn_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_udp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/ryu_controller_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/__pycache__/ryu_controller_app.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/restart_remote_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/test_flow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/check_remote.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/conftest.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test.sh  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_detailed.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_execution.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_run.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test_detailed.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_test_with_logging.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/update_test_configs.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/test_remote_datasets.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/test_remote_datasets.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_tests.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test_simple.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_with_password.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/setup_and_run_remote.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/test_example.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/test_example.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/remote_test_utils.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/conftest.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/test_remote_datasets.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/simple_ssh_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/test_remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/generate_cicflow_dataset.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/process_pcap_to_csv.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/ssh_connect.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/main.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/test_ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/topology.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/minimal_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_all.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_basic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_env.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_remote_connection.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/ssh_test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_2.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_3.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_4.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221836.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221841.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221909.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_202736.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_220533.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221836.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221841.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221909.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/minimal_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc
2025-07-09 02:50:56,859 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 02:50:56,860 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 02:50:56,860 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 02:50:56,949 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 02:50:56,950 - DEBUG - Secsh channel 3 opened.
2025-07-09 02:50:56,971 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 02:50:57,041 - DEBUG - [chan 3] EOF received (3)
2025-07-09 02:50:57,042 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 02:50:57,043 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 02:50:57,044 - DEBUG - STDOUT: 
2025-07-09 02:50:57,045 - DEBUG - Executing command: cd ~/dataset_generation && pwd
2025-07-09 02:50:57,045 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 02:50:57,139 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 02:50:57,139 - DEBUG - Secsh channel 4 opened.
2025-07-09 02:50:57,160 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 02:50:57,230 - DEBUG - [chan 4] EOF received (4)
2025-07-09 02:50:57,230 - INFO - Command executed successfully: cd ~/dataset_generation && pwd
2025-07-09 02:50:57,230 - DEBUG - STDOUT: /home/<USER>/dataset_generation
2025-07-09 02:50:57,230 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 02:50:57,230 - DEBUG - Executing command: python main.py
2025-07-09 02:50:57,231 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 02:50:57,311 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 02:50:57,311 - DEBUG - Secsh channel 5 opened.
2025-07-09 02:50:57,332 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 02:50:57,354 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 02:50:57,420 - DEBUG - [chan 5] EOF received (5)
2025-07-09 02:50:57,420 - ERROR - Command failed with exit code 2: python main.py
2025-07-09 02:50:57,420 - ERROR - STDERR: 
2025-07-09 02:50:57,420 - DEBUG - [chan 5] EOF sent (5)
2025-07-09 02:50:57,420 - INFO - [chan 0] sftp session closed.
2025-07-09 02:50:57,420 - DEBUG - [chan 0] EOF sent (0)
2025-07-09 02:50:57,421 - DEBUG - EOF in transport thread
2025-07-09 02:51:07,869 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 02:51:07,869 - DEBUG - Using password authentication
2025-07-09 02:51:07,934 - DEBUG - starting thread (client mode): 0xfa2cfce0
2025-07-09 02:51:07,934 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 02:51:07,958 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 02:51:07,958 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 02:51:07,979 - DEBUG - === Key exchange possibilities ===
2025-07-09 02:51:07,980 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 02:51:07,980 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 02:51:07,981 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:51:07,981 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:51:07,981 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:51:07,982 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:51:07,982 - DEBUG - client compress: none, <EMAIL>
2025-07-09 02:51:07,983 - DEBUG - server compress: none, <EMAIL>
2025-07-09 02:51:07,983 - DEBUG - client lang: <none>
2025-07-09 02:51:07,983 - DEBUG - server lang: <none>
2025-07-09 02:51:07,984 - DEBUG - kex follows: False
2025-07-09 02:51:07,984 - DEBUG - === Key exchange agreements ===
2025-07-09 02:51:07,984 - DEBUG - Kex: <EMAIL>
2025-07-09 02:51:07,985 - DEBUG - HostKey: ssh-ed25519
2025-07-09 02:51:07,985 - DEBUG - Cipher: aes128-ctr
2025-07-09 02:51:07,985 - DEBUG - MAC: hmac-sha2-256
2025-07-09 02:51:07,985 - DEBUG - Compression: none
2025-07-09 02:51:07,985 - DEBUG - === End of kex handshake ===
2025-07-09 02:51:08,012 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 02:51:08,013 - DEBUG - Switch to new keys ...
2025-07-09 02:51:08,013 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 02:51:08,013 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 02:51:08,099 - DEBUG - userauth is OK
2025-07-09 02:51:08,153 - INFO - Authentication (password) successful!
2025-07-09 02:51:08,153 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 02:51:08,247 - DEBUG - Received global request "<EMAIL>"
2025-07-09 02:51:08,248 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 02:51:08,318 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 02:51:08,318 - DEBUG - Secsh channel 0 opened.
2025-07-09 02:51:08,338 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 02:51:08,358 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 02:51:08,360 - INFO - SSH connection established successfully
2025-07-09 02:51:08,360 - DEBUG - Executing command: echo $HOME
2025-07-09 02:51:08,360 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 02:51:08,384 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 02:51:08,384 - DEBUG - Secsh channel 1 opened.
2025-07-09 02:51:08,407 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 02:51:08,477 - DEBUG - [chan 1] EOF received (1)
2025-07-09 02:51:08,477 - INFO - Command executed successfully: echo $HOME
2025-07-09 02:51:08,477 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 02:51:08,478 - DEBUG - STDOUT: /home/<USER>
2025-07-09 02:51:08,588 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 02:51:08,634 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 02:51:08,897 - DEBUG - [chan 0] close(00000000)
2025-07-09 02:51:08,963 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 02:51:08,982 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:51:08,982 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 02:51:09,001 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 02:51:09,001 - DEBUG - Secsh channel 2 opened.
2025-07-09 02:51:09,022 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 02:51:09,111 - DEBUG - [chan 2] EOF received (2)
2025-07-09 02:51:09,111 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:51:09,111 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 02:51:09,111 - DEBUG - STDOUT: Archive:  /home/<USER>/project.zip
  inflating: /home/<USER>/dataset_generation/dataset_generation/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/project.zip  
  inflating: /home/<USER>/dataset_generation/dataset_generation/pytest.ini  
  inflating: /home/<USER>/dataset_generation/dataset_generation/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_remote_generation.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/setup.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test-requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/TESTING.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_debug.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/.gitignore  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/CACHEDIR.TAG  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/lastfailed  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/nodeids  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/analysis.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/install.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/progress.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/scenario.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/summary.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/app.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220519.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220937.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/ssh_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/topology.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen-attack-script.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_advanced_adversarial_ddos_attacks.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_icmp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_syn_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_udp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow-generic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_icmp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_syn_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_udp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/ryu_controller_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/__pycache__/ryu_controller_app.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/restart_remote_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/test_flow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/check_remote.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/conftest.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test.sh  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_detailed.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_execution.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_run.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test_detailed.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_test_with_logging.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/update_test_configs.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/test_remote_datasets.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/test_remote_datasets.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_tests.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test_simple.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_with_password.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/setup_and_run_remote.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/test_example.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/test_example.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/remote_test_utils.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/conftest.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/test_remote_datasets.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/simple_ssh_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/test_remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/generate_cicflow_dataset.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/process_pcap_to_csv.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/ssh_connect.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/main.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/test_ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/topology.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/minimal_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_all.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_basic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_env.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_remote_connection.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/ssh_test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_2.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_3.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_4.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221836.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221841.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221909.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_202736.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_220533.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221836.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221841.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221909.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/minimal_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc
2025-07-09 02:51:09,112 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 02:51:09,112 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 02:51:09,204 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 02:51:09,204 - DEBUG - Secsh channel 3 opened.
2025-07-09 02:51:09,223 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 02:51:09,296 - DEBUG - [chan 3] EOF received (3)
2025-07-09 02:51:09,298 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 02:51:09,298 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 02:51:09,299 - DEBUG - STDOUT: 
2025-07-09 02:51:09,300 - DEBUG - Executing command: cd /home/<USER>/dataset_generation && pwd
2025-07-09 02:51:09,300 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 02:51:09,388 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 02:51:09,388 - DEBUG - Secsh channel 4 opened.
2025-07-09 02:51:09,413 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 02:51:09,487 - DEBUG - [chan 4] EOF received (4)
2025-07-09 02:51:09,487 - INFO - Command executed successfully: cd /home/<USER>/dataset_generation && pwd
2025-07-09 02:51:09,487 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 02:51:09,487 - DEBUG - STDOUT: /home/<USER>/dataset_generation
2025-07-09 02:51:09,488 - DEBUG - Executing command: python main.py
2025-07-09 02:51:09,488 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 02:51:09,567 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 02:51:09,567 - DEBUG - Secsh channel 5 opened.
2025-07-09 02:51:09,587 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 02:51:09,614 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 02:51:09,674 - DEBUG - [chan 5] EOF received (5)
2025-07-09 02:51:09,675 - ERROR - Command failed with exit code 2: python main.py
2025-07-09 02:51:09,675 - DEBUG - [chan 5] EOF sent (5)
2025-07-09 02:51:09,675 - ERROR - STDERR: 
2025-07-09 02:51:09,676 - INFO - [chan 0] sftp session closed.
2025-07-09 02:51:09,676 - DEBUG - [chan 0] EOF sent (0)
2025-07-09 02:51:09,676 - DEBUG - EOF in transport thread
2025-07-09 02:51:25,294 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 02:51:25,294 - DEBUG - Using password authentication
2025-07-09 02:51:25,364 - DEBUG - starting thread (client mode): 0x6be6660
2025-07-09 02:51:25,364 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 02:51:25,395 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 02:51:25,396 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 02:51:25,463 - DEBUG - === Key exchange possibilities ===
2025-07-09 02:51:25,463 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 02:51:25,463 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 02:51:25,463 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:51:25,464 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:51:25,464 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:51:25,464 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:51:25,464 - DEBUG - client compress: none, <EMAIL>
2025-07-09 02:51:25,464 - DEBUG - server compress: none, <EMAIL>
2025-07-09 02:51:25,464 - DEBUG - client lang: <none>
2025-07-09 02:51:25,464 - DEBUG - server lang: <none>
2025-07-09 02:51:25,465 - DEBUG - kex follows: False
2025-07-09 02:51:25,465 - DEBUG - === Key exchange agreements ===
2025-07-09 02:51:25,465 - DEBUG - Kex: <EMAIL>
2025-07-09 02:51:25,465 - DEBUG - HostKey: ssh-ed25519
2025-07-09 02:51:25,465 - DEBUG - Cipher: aes128-ctr
2025-07-09 02:51:25,465 - DEBUG - MAC: hmac-sha2-256
2025-07-09 02:51:25,466 - DEBUG - Compression: none
2025-07-09 02:51:25,466 - DEBUG - === End of kex handshake ===
2025-07-09 02:51:25,551 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 02:51:25,553 - DEBUG - Switch to new keys ...
2025-07-09 02:51:25,554 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 02:51:25,554 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 02:51:25,632 - DEBUG - userauth is OK
2025-07-09 02:51:25,653 - INFO - Authentication (password) successful!
2025-07-09 02:51:25,654 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 02:51:25,753 - DEBUG - Received global request "<EMAIL>"
2025-07-09 02:51:25,753 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 02:51:25,825 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 02:51:25,826 - DEBUG - Secsh channel 0 opened.
2025-07-09 02:51:25,851 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 02:51:25,872 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 02:51:25,873 - INFO - SSH connection established successfully
2025-07-09 02:51:25,873 - DEBUG - Executing command: echo $HOME
2025-07-09 02:51:25,874 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 02:51:25,897 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 02:51:25,897 - DEBUG - Secsh channel 1 opened.
2025-07-09 02:51:25,918 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 02:51:25,984 - DEBUG - [chan 1] EOF received (1)
2025-07-09 02:51:25,985 - INFO - Command executed successfully: echo $HOME
2025-07-09 02:51:25,986 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 02:51:25,986 - DEBUG - STDOUT: /home/<USER>
2025-07-09 02:51:26,131 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 02:51:26,152 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 02:51:26,408 - DEBUG - [chan 0] close(00000000)
2025-07-09 02:51:26,481 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 02:51:26,500 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:51:26,501 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 02:51:26,528 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 02:51:26,529 - DEBUG - Secsh channel 2 opened.
2025-07-09 02:51:26,549 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 02:51:26,612 - DEBUG - [chan 2] EOF received (2)
2025-07-09 02:51:26,612 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:51:26,612 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 02:51:26,612 - DEBUG - STDOUT: Archive:  /home/<USER>/project.zip
  inflating: /home/<USER>/dataset_generation/dataset_generation/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/project.zip  
  inflating: /home/<USER>/dataset_generation/dataset_generation/pytest.ini  
  inflating: /home/<USER>/dataset_generation/dataset_generation/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_remote_generation.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/setup.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test-requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/TESTING.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_debug.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/.gitignore  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/CACHEDIR.TAG  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/lastfailed  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/nodeids  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/analysis.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/install.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/progress.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/scenario.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/summary.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/app.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220519.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220937.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/ssh_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/topology.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen-attack-script.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_advanced_adversarial_ddos_attacks.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_icmp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_syn_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_udp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow-generic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_icmp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_syn_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_udp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/ryu_controller_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/__pycache__/ryu_controller_app.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/restart_remote_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/test_flow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/check_remote.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/conftest.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test.sh  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_detailed.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_execution.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_run.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test_detailed.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_test_with_logging.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/update_test_configs.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/test_remote_datasets.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/test_remote_datasets.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_tests.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test_simple.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_with_password.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/setup_and_run_remote.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/test_example.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/test_example.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/remote_test_utils.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/conftest.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/test_remote_datasets.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/simple_ssh_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/test_remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/generate_cicflow_dataset.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/process_pcap_to_csv.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/ssh_connect.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/main.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/test_ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/topology.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/minimal_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_all.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_basic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_env.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_remote_connection.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/ssh_test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_2.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_3.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_4.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221836.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221841.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221909.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_202736.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_220533.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221836.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221841.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221909.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/minimal_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc
2025-07-09 02:51:26,613 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 02:51:26,613 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 02:51:26,693 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 02:51:26,693 - DEBUG - Secsh channel 3 opened.
2025-07-09 02:51:26,715 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 02:51:26,784 - DEBUG - [chan 3] EOF received (3)
2025-07-09 02:51:26,786 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 02:51:26,786 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 02:51:26,787 - DEBUG - STDOUT: 
2025-07-09 02:51:26,788 - DEBUG - Executing command: cd /home/<USER>/dataset_generation && python main.py
2025-07-09 02:51:26,789 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 02:51:26,876 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 02:51:26,876 - DEBUG - Secsh channel 4 opened.
2025-07-09 02:51:26,895 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 02:51:26,920 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 02:51:26,990 - DEBUG - [chan 4] EOF received (4)
2025-07-09 02:51:26,991 - ERROR - Command failed with exit code 2: cd /home/<USER>/dataset_generation && python main.py
2025-07-09 02:51:26,992 - ERROR - STDERR: 
2025-07-09 02:51:26,992 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 02:51:26,994 - INFO - [chan 0] sftp session closed.
2025-07-09 02:51:26,994 - DEBUG - [chan 0] EOF sent (0)
2025-07-09 02:51:26,996 - DEBUG - EOF in transport thread
2025-07-09 02:51:37,972 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 02:51:37,972 - DEBUG - Using password authentication
2025-07-09 02:51:38,052 - DEBUG - starting thread (client mode): 0xb63f9df0
2025-07-09 02:51:38,053 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 02:51:38,060 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 02:51:38,060 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 02:51:38,133 - DEBUG - === Key exchange possibilities ===
2025-07-09 02:51:38,133 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 02:51:38,133 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 02:51:38,133 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:51:38,133 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:51:38,133 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:51:38,133 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:51:38,133 - DEBUG - client compress: none, <EMAIL>
2025-07-09 02:51:38,133 - DEBUG - server compress: none, <EMAIL>
2025-07-09 02:51:38,133 - DEBUG - client lang: <none>
2025-07-09 02:51:38,133 - DEBUG - server lang: <none>
2025-07-09 02:51:38,133 - DEBUG - kex follows: False
2025-07-09 02:51:38,133 - DEBUG - === Key exchange agreements ===
2025-07-09 02:51:38,134 - DEBUG - Kex: <EMAIL>
2025-07-09 02:51:38,134 - DEBUG - HostKey: ssh-ed25519
2025-07-09 02:51:38,134 - DEBUG - Cipher: aes128-ctr
2025-07-09 02:51:38,134 - DEBUG - MAC: hmac-sha2-256
2025-07-09 02:51:38,134 - DEBUG - Compression: none
2025-07-09 02:51:38,134 - DEBUG - === End of kex handshake ===
2025-07-09 02:51:38,159 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 02:51:38,160 - DEBUG - Switch to new keys ...
2025-07-09 02:51:38,160 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 02:51:38,160 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 02:51:38,242 - DEBUG - userauth is OK
2025-07-09 02:51:38,267 - INFO - Authentication (password) successful!
2025-07-09 02:51:38,268 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 02:51:38,362 - DEBUG - Received global request "<EMAIL>"
2025-07-09 02:51:38,362 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 02:51:38,431 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 02:51:38,431 - DEBUG - Secsh channel 0 opened.
2025-07-09 02:51:38,451 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 02:51:38,470 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 02:51:38,471 - INFO - SSH connection established successfully
2025-07-09 02:51:38,471 - DEBUG - Executing command: echo $HOME
2025-07-09 02:51:38,471 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 02:51:38,491 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 02:51:38,491 - DEBUG - Secsh channel 1 opened.
2025-07-09 02:51:38,513 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 02:51:38,574 - DEBUG - [chan 1] EOF received (1)
2025-07-09 02:51:38,576 - INFO - Command executed successfully: echo $HOME
2025-07-09 02:51:38,576 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 02:51:38,577 - DEBUG - STDOUT: /home/<USER>
2025-07-09 02:51:38,688 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 02:51:38,732 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 02:51:39,003 - DEBUG - [chan 0] close(00000000)
2025-07-09 02:51:39,061 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 02:51:39,081 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:51:39,081 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 02:51:39,114 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 02:51:39,114 - DEBUG - Secsh channel 2 opened.
2025-07-09 02:51:39,148 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 02:51:39,195 - DEBUG - [chan 2] EOF received (2)
2025-07-09 02:51:39,195 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:51:39,196 - DEBUG - STDOUT: Archive:  /home/<USER>/project.zip
  inflating: /home/<USER>/dataset_generation/dataset_generation/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/project.zip  
  inflating: /home/<USER>/dataset_generation/dataset_generation/pytest.ini  
  inflating: /home/<USER>/dataset_generation/dataset_generation/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_remote_generation.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/setup.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test-requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/TESTING.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_debug.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/.gitignore  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/CACHEDIR.TAG  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/lastfailed  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/nodeids  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/analysis.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/install.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/progress.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/scenario.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/summary.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/app.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220519.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220937.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/ssh_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/topology.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen-attack-script.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_advanced_adversarial_ddos_attacks.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_icmp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_syn_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_udp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow-generic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_icmp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_syn_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_udp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/ryu_controller_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/__pycache__/ryu_controller_app.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/restart_remote_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/test_flow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/check_remote.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/conftest.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test.sh  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_detailed.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_execution.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_run.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test_detailed.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_test_with_logging.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/update_test_configs.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/test_remote_datasets.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/test_remote_datasets.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_tests.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test_simple.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_with_password.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/setup_and_run_remote.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/test_example.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/test_example.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/remote_test_utils.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/conftest.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/test_remote_datasets.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/simple_ssh_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/test_remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/generate_cicflow_dataset.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/process_pcap_to_csv.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/ssh_connect.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/main.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/test_ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/topology.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/minimal_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_all.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_basic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_env.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_remote_connection.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/ssh_test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_2.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_3.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_4.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221836.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221841.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221909.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_202736.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_220533.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221836.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221841.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221909.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/minimal_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc
2025-07-09 02:51:39,196 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 02:51:39,196 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 02:51:39,196 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 02:51:39,278 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 02:51:39,279 - DEBUG - Secsh channel 3 opened.
2025-07-09 02:51:39,300 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 02:51:39,364 - DEBUG - [chan 3] EOF received (3)
2025-07-09 02:51:39,365 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 02:51:39,366 - DEBUG - STDOUT: 
2025-07-09 02:51:39,366 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 02:51:39,367 - DEBUG - Executing command: python /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 02:51:39,368 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 02:51:39,450 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 02:51:39,451 - DEBUG - Secsh channel 4 opened.
2025-07-09 02:51:39,470 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 02:51:39,490 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 02:51:39,552 - DEBUG - [chan 4] EOF received (4)
2025-07-09 02:51:39,552 - ERROR - Command failed with exit code 1: python /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 02:51:39,552 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 02:51:39,552 - ERROR - STDERR: 
2025-07-09 02:51:39,553 - INFO - [chan 0] sftp session closed.
2025-07-09 02:51:39,553 - DEBUG - [chan 0] EOF sent (0)
2025-07-09 02:51:39,553 - DEBUG - EOF in transport thread
2025-07-09 02:51:55,885 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 02:51:55,885 - DEBUG - Using password authentication
2025-07-09 02:51:56,003 - DEBUG - starting thread (client mode): 0x9f93fc80
2025-07-09 02:51:56,004 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 02:51:56,027 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 02:51:56,027 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 02:51:56,048 - DEBUG - === Key exchange possibilities ===
2025-07-09 02:51:56,048 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 02:51:56,048 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 02:51:56,048 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:51:56,048 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:51:56,048 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:51:56,049 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:51:56,049 - DEBUG - client compress: none, <EMAIL>
2025-07-09 02:51:56,049 - DEBUG - server compress: none, <EMAIL>
2025-07-09 02:51:56,049 - DEBUG - client lang: <none>
2025-07-09 02:51:56,049 - DEBUG - server lang: <none>
2025-07-09 02:51:56,049 - DEBUG - kex follows: False
2025-07-09 02:51:56,049 - DEBUG - === Key exchange agreements ===
2025-07-09 02:51:56,050 - DEBUG - Kex: <EMAIL>
2025-07-09 02:51:56,050 - DEBUG - HostKey: ssh-ed25519
2025-07-09 02:51:56,050 - DEBUG - Cipher: aes128-ctr
2025-07-09 02:51:56,050 - DEBUG - MAC: hmac-sha2-256
2025-07-09 02:51:56,050 - DEBUG - Compression: none
2025-07-09 02:51:56,051 - DEBUG - === End of kex handshake ===
2025-07-09 02:51:56,078 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 02:51:56,080 - DEBUG - Switch to new keys ...
2025-07-09 02:51:56,082 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 02:51:56,082 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 02:51:56,165 - DEBUG - userauth is OK
2025-07-09 02:51:56,185 - INFO - Authentication (password) successful!
2025-07-09 02:51:56,186 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 02:51:56,283 - DEBUG - Received global request "<EMAIL>"
2025-07-09 02:51:56,283 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 02:51:56,348 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 02:51:56,349 - DEBUG - Secsh channel 0 opened.
2025-07-09 02:51:56,369 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 02:51:56,390 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 02:51:56,391 - INFO - SSH connection established successfully
2025-07-09 02:51:56,391 - DEBUG - Executing command: echo $HOME
2025-07-09 02:51:56,392 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 02:51:56,417 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 02:51:56,418 - DEBUG - Secsh channel 1 opened.
2025-07-09 02:51:56,439 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 02:51:56,505 - DEBUG - [chan 1] EOF received (1)
2025-07-09 02:51:56,506 - INFO - Command executed successfully: echo $HOME
2025-07-09 02:51:56,507 - DEBUG - STDOUT: /home/<USER>
2025-07-09 02:51:56,507 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 02:51:56,630 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 02:51:56,667 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 02:51:56,942 - DEBUG - [chan 0] close(00000000)
2025-07-09 02:51:57,000 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 02:51:57,026 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:51:57,026 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 02:51:57,047 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 02:51:57,048 - DEBUG - Secsh channel 2 opened.
2025-07-09 02:51:57,069 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 02:51:57,129 - DEBUG - [chan 2] EOF received (2)
2025-07-09 02:51:57,130 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:51:57,130 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 02:51:57,135 - DEBUG - STDOUT: Archive:  /home/<USER>/project.zip
  inflating: /home/<USER>/dataset_generation/dataset_generation/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/project.zip  
  inflating: /home/<USER>/dataset_generation/dataset_generation/pytest.ini  
  inflating: /home/<USER>/dataset_generation/dataset_generation/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_remote_generation.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/setup.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test-requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/TESTING.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_debug.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/.gitignore  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/CACHEDIR.TAG  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/lastfailed  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/nodeids  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/analysis.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/install.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/progress.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/scenario.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/summary.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/app.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220519.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220937.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/ssh_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/topology.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen-attack-script.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_advanced_adversarial_ddos_attacks.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_icmp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_syn_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_udp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow-generic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_icmp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_syn_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_udp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/ryu_controller_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/__pycache__/ryu_controller_app.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/restart_remote_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/test_flow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/check_remote.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/conftest.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test.sh  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_detailed.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_execution.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_run.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test_detailed.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_test_with_logging.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/update_test_configs.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/test_remote_datasets.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/test_remote_datasets.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_tests.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test_simple.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_with_password.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/setup_and_run_remote.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/test_example.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/test_example.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/remote_test_utils.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/conftest.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/test_remote_datasets.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/simple_ssh_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/test_remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/generate_cicflow_dataset.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/process_pcap_to_csv.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/ssh_connect.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/main.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/test_ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/topology.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/minimal_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_all.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_basic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_env.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_remote_connection.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/ssh_test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_2.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_3.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_4.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221836.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221841.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221909.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_202736.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_220533.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221836.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221841.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221909.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/minimal_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc
2025-07-09 02:51:57,135 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 02:51:57,135 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 02:51:57,221 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 02:51:57,221 - DEBUG - Secsh channel 3 opened.
2025-07-09 02:51:57,249 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 02:51:57,308 - DEBUG - [chan 3] EOF received (3)
2025-07-09 02:51:57,309 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 02:51:57,309 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 02:51:57,310 - DEBUG - STDOUT: 
2025-07-09 02:51:57,310 - DEBUG - Executing command: python3 --version
2025-07-09 02:51:57,311 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 02:51:57,389 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 02:51:57,389 - DEBUG - Secsh channel 4 opened.
2025-07-09 02:51:57,408 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 02:51:57,483 - DEBUG - [chan 4] EOF received (4)
2025-07-09 02:51:57,484 - INFO - Command executed successfully: python3 --version
2025-07-09 02:51:57,484 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 02:51:57,485 - DEBUG - STDOUT: Python 3.6.9
2025-07-09 02:51:57,486 - DEBUG - Executing command: python /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 02:51:57,487 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 02:51:57,565 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 02:51:57,565 - DEBUG - Secsh channel 5 opened.
2025-07-09 02:51:57,595 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 02:51:57,616 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 02:51:57,698 - DEBUG - [chan 5] EOF received (5)
2025-07-09 02:51:57,698 - ERROR - Command failed with exit code 1: python /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 02:51:57,699 - ERROR - STDERR: 
2025-07-09 02:51:57,700 - DEBUG - [chan 5] EOF sent (5)
2025-07-09 02:51:57,701 - INFO - [chan 0] sftp session closed.
2025-07-09 02:51:57,702 - DEBUG - [chan 0] EOF sent (0)
2025-07-09 02:51:57,704 - DEBUG - EOF in transport thread
2025-07-09 02:52:08,309 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 02:52:08,309 - DEBUG - Using password authentication
2025-07-09 02:52:08,377 - DEBUG - starting thread (client mode): 0x92533170
2025-07-09 02:52:08,377 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 02:52:08,382 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 02:52:08,382 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 02:52:08,442 - DEBUG - === Key exchange possibilities ===
2025-07-09 02:52:08,443 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 02:52:08,443 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 02:52:08,443 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:52:08,443 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:52:08,443 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:52:08,443 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:52:08,443 - DEBUG - client compress: none, <EMAIL>
2025-07-09 02:52:08,443 - DEBUG - server compress: none, <EMAIL>
2025-07-09 02:52:08,443 - DEBUG - client lang: <none>
2025-07-09 02:52:08,444 - DEBUG - server lang: <none>
2025-07-09 02:52:08,444 - DEBUG - kex follows: False
2025-07-09 02:52:08,444 - DEBUG - === Key exchange agreements ===
2025-07-09 02:52:08,444 - DEBUG - Kex: <EMAIL>
2025-07-09 02:52:08,444 - DEBUG - HostKey: ssh-ed25519
2025-07-09 02:52:08,444 - DEBUG - Cipher: aes128-ctr
2025-07-09 02:52:08,444 - DEBUG - MAC: hmac-sha2-256
2025-07-09 02:52:08,444 - DEBUG - Compression: none
2025-07-09 02:52:08,444 - DEBUG - === End of kex handshake ===
2025-07-09 02:52:08,533 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 02:52:08,535 - DEBUG - Switch to new keys ...
2025-07-09 02:52:08,537 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 02:52:08,538 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 02:52:08,620 - DEBUG - userauth is OK
2025-07-09 02:52:08,642 - INFO - Authentication (password) successful!
2025-07-09 02:52:08,643 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 02:52:08,740 - DEBUG - Received global request "<EMAIL>"
2025-07-09 02:52:08,741 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 02:52:08,806 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 02:52:08,806 - DEBUG - Secsh channel 0 opened.
2025-07-09 02:52:08,827 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 02:52:08,848 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 02:52:08,848 - INFO - SSH connection established successfully
2025-07-09 02:52:08,848 - DEBUG - Executing command: echo $HOME
2025-07-09 02:52:08,848 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 02:52:08,868 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 02:52:08,868 - DEBUG - Secsh channel 1 opened.
2025-07-09 02:52:08,888 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 02:52:08,964 - DEBUG - [chan 1] EOF received (1)
2025-07-09 02:52:08,964 - INFO - Command executed successfully: echo $HOME
2025-07-09 02:52:08,965 - DEBUG - STDOUT: /home/<USER>
2025-07-09 02:52:08,966 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 02:52:09,114 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 02:52:09,134 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 02:52:09,418 - DEBUG - [chan 0] close(00000000)
2025-07-09 02:52:09,493 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 02:52:09,518 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:52:09,519 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 02:52:09,540 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 02:52:09,541 - DEBUG - Secsh channel 2 opened.
2025-07-09 02:52:09,562 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 02:52:09,623 - DEBUG - [chan 2] EOF received (2)
2025-07-09 02:52:09,624 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:52:09,624 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 02:52:09,624 - DEBUG - STDOUT: Archive:  /home/<USER>/project.zip
  inflating: /home/<USER>/dataset_generation/dataset_generation/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/project.zip  
  inflating: /home/<USER>/dataset_generation/dataset_generation/pytest.ini  
  inflating: /home/<USER>/dataset_generation/dataset_generation/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_remote_generation.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/setup.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test-requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/TESTING.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_debug.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/.gitignore  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/CACHEDIR.TAG  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/lastfailed  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/nodeids  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/analysis.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/install.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/progress.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/scenario.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/summary.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/app.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220519.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220937.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/ssh_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/topology.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen-attack-script.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_advanced_adversarial_ddos_attacks.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_icmp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_syn_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_udp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow-generic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_icmp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_syn_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_udp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/ryu_controller_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/__pycache__/ryu_controller_app.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/restart_remote_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/test_flow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/check_remote.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/conftest.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test.sh  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_detailed.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_execution.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_run.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test_detailed.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_test_with_logging.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/update_test_configs.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/test_remote_datasets.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/test_remote_datasets.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_tests.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test_simple.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_with_password.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/setup_and_run_remote.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/test_example.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/test_example.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/remote_test_utils.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/conftest.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/test_remote_datasets.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/simple_ssh_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/test_remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/generate_cicflow_dataset.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/process_pcap_to_csv.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/ssh_connect.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/main.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/test_ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/topology.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/minimal_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_all.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_basic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_env.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_remote_connection.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/ssh_test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_2.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_3.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_4.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221836.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221841.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221909.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_202736.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_220533.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221836.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221841.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221909.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/minimal_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc
2025-07-09 02:52:09,624 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 02:52:09,624 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 02:52:09,710 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 02:52:09,710 - DEBUG - Secsh channel 3 opened.
2025-07-09 02:52:09,730 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 02:52:09,801 - DEBUG - [chan 3] EOF received (3)
2025-07-09 02:52:09,801 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 02:52:09,801 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 02:52:09,801 - DEBUG - STDOUT: 
2025-07-09 02:52:09,802 - DEBUG - Executing command: python3 --version
2025-07-09 02:52:09,802 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 02:52:09,882 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 02:52:09,883 - DEBUG - Secsh channel 4 opened.
2025-07-09 02:52:09,903 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 02:52:09,973 - DEBUG - [chan 4] EOF received (4)
2025-07-09 02:52:09,973 - INFO - Command executed successfully: python3 --version
2025-07-09 02:52:09,973 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 02:52:09,973 - DEBUG - STDOUT: Python 3.6.9
2025-07-09 02:52:09,974 - DEBUG - Executing command: python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 02:52:09,974 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 02:52:10,054 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 02:52:10,055 - DEBUG - Secsh channel 5 opened.
2025-07-09 02:52:10,076 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 02:52:10,164 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 02:52:10,399 - DEBUG - [chan 5] EOF received (5)
2025-07-09 02:52:10,399 - ERROR - Command failed with exit code 1: python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 02:52:10,399 - ERROR - STDERR: 
2025-07-09 02:52:10,399 - DEBUG - [chan 5] EOF sent (5)
2025-07-09 02:52:10,400 - INFO - [chan 0] sftp session closed.
2025-07-09 02:52:10,400 - DEBUG - [chan 0] EOF sent (0)
2025-07-09 02:52:10,400 - DEBUG - EOF in transport thread
2025-07-09 02:52:21,688 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 02:52:21,688 - DEBUG - Using password authentication
2025-07-09 02:52:21,761 - DEBUG - starting thread (client mode): 0x3ee0fe30
2025-07-09 02:52:21,761 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 02:52:21,766 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 02:52:21,767 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 02:52:21,831 - DEBUG - === Key exchange possibilities ===
2025-07-09 02:52:21,831 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 02:52:21,832 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 02:52:21,832 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:52:21,832 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:52:21,832 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:52:21,833 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:52:21,833 - DEBUG - client compress: none, <EMAIL>
2025-07-09 02:52:21,835 - DEBUG - server compress: none, <EMAIL>
2025-07-09 02:52:21,835 - DEBUG - client lang: <none>
2025-07-09 02:52:21,835 - DEBUG - server lang: <none>
2025-07-09 02:52:21,836 - DEBUG - kex follows: False
2025-07-09 02:52:21,836 - DEBUG - === Key exchange agreements ===
2025-07-09 02:52:21,836 - DEBUG - Kex: <EMAIL>
2025-07-09 02:52:21,836 - DEBUG - HostKey: ssh-ed25519
2025-07-09 02:52:21,836 - DEBUG - Cipher: aes128-ctr
2025-07-09 02:52:21,837 - DEBUG - MAC: hmac-sha2-256
2025-07-09 02:52:21,837 - DEBUG - Compression: none
2025-07-09 02:52:21,837 - DEBUG - === End of kex handshake ===
2025-07-09 02:52:21,910 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 02:52:21,910 - DEBUG - Switch to new keys ...
2025-07-09 02:52:21,911 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 02:52:21,911 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 02:52:21,998 - DEBUG - userauth is OK
2025-07-09 02:52:22,021 - INFO - Authentication (password) successful!
2025-07-09 02:52:22,022 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 02:52:22,117 - DEBUG - Received global request "<EMAIL>"
2025-07-09 02:52:22,117 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 02:52:22,189 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 02:52:22,189 - DEBUG - Secsh channel 0 opened.
2025-07-09 02:52:22,215 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 02:52:22,238 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 02:52:22,239 - INFO - SSH connection established successfully
2025-07-09 02:52:22,239 - DEBUG - Executing command: echo $HOME
2025-07-09 02:52:22,239 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 02:52:22,259 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 02:52:22,259 - DEBUG - Secsh channel 1 opened.
2025-07-09 02:52:22,279 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 02:52:22,350 - DEBUG - [chan 1] EOF received (1)
2025-07-09 02:52:22,352 - INFO - Command executed successfully: echo $HOME
2025-07-09 02:52:22,353 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 02:52:22,353 - DEBUG - STDOUT: /home/<USER>
2025-07-09 02:52:22,479 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 02:52:22,544 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 02:52:22,808 - DEBUG - [chan 0] close(00000000)
2025-07-09 02:52:22,871 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 02:52:22,891 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:52:22,891 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 02:52:22,910 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 02:52:22,910 - DEBUG - Secsh channel 2 opened.
2025-07-09 02:52:22,933 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 02:52:22,979 - DEBUG - [chan 2] EOF received (2)
2025-07-09 02:52:22,979 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:52:22,980 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 02:52:22,980 - DEBUG - STDOUT: Archive:  /home/<USER>/project.zip
  inflating: /home/<USER>/dataset_generation/dataset_generation/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/project.zip  
  inflating: /home/<USER>/dataset_generation/dataset_generation/pytest.ini  
  inflating: /home/<USER>/dataset_generation/dataset_generation/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_remote_generation.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/setup.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test-requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/TESTING.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_debug.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/.gitignore  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/CACHEDIR.TAG  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/lastfailed  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/nodeids  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/analysis.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/install.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/progress.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/scenario.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/summary.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/app.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220519.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220937.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/ssh_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/topology.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen-attack-script.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_advanced_adversarial_ddos_attacks.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_icmp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_syn_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_udp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow-generic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_icmp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_syn_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_udp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/ryu_controller_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/__pycache__/ryu_controller_app.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/restart_remote_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/test_flow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/check_remote.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/conftest.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test.sh  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_detailed.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_execution.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_run.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test_detailed.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_test_with_logging.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/update_test_configs.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/test_remote_datasets.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/test_remote_datasets.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_tests.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test_simple.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_with_password.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/setup_and_run_remote.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/test_example.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/test_example.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/remote_test_utils.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/conftest.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/test_remote_datasets.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/simple_ssh_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/test_remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/generate_cicflow_dataset.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/process_pcap_to_csv.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/ssh_connect.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/main.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/test_ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/topology.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/minimal_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_all.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_basic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_env.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_remote_connection.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/ssh_test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_2.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_3.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_4.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221836.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221841.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221909.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_202736.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_220533.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221836.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221841.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221909.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/minimal_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc
2025-07-09 02:52:22,980 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 02:52:22,981 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 02:52:23,068 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 02:52:23,069 - DEBUG - Secsh channel 3 opened.
2025-07-09 02:52:23,092 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 02:52:23,155 - DEBUG - [chan 3] EOF received (3)
2025-07-09 02:52:23,156 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 02:52:23,156 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 02:52:23,156 - DEBUG - STDOUT: 
2025-07-09 02:52:23,156 - DEBUG - Executing command: python3 -m pip install --upgrade pip requests urllib3 pyOpenSSL cryptography
2025-07-09 02:52:23,156 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 02:52:23,242 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 02:52:23,243 - DEBUG - Secsh channel 4 opened.
2025-07-09 02:52:23,268 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 02:52:31,417 - DEBUG - [chan 4] EOF received (4)
2025-07-09 02:52:31,418 - INFO - Command executed successfully: python3 -m pip install --upgrade pip requests urllib3 pyOpenSSL cryptography
2025-07-09 02:52:31,419 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 02:52:31,419 - DEBUG - STDOUT: Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: pip in /usr/local/lib/python3.6/dist-packages (21.3.1)
Requirement already satisfied: requests in /usr/lib/python3/dist-packages (2.18.4)
Collecting requests
  Using cached requests-2.27.1-py2.py3-none-any.whl (63 kB)
Requirement already satisfied: urllib3 in /usr/lib/python3/dist-packages (1.22)
Collecting urllib3
  Using cached urllib3-1.26.20-py2.py3-none-any.whl (144 kB)
Requirement already satisfied: pyOpenSSL in /usr/lib/python3/dist-packages (17.5.0)
Collecting pyOpenSSL
  Downloading pyOpenSSL-23.2.0-py3-none-any.whl (59 kB)
Requirement already satisfied: cryptography in /usr/local/lib/python3.6/dist-packages (40.0.2)
Collecting charset-normalizer~=2.0.0
  Downloading charset_normalizer-2.0.12-py3-none-any.whl (39 kB)
Requirement already satisfied: certifi>=2017.4.17 in /usr/lib/python3/dist-packages (from requests) (2018.1.18)
Requirement already satisfied: idna<4,>=2.5 in /usr/lib/python3/dist-packages (from requests) (2.6)
Requirement already satisfied: cffi>=1.12 in /usr/local/lib/python3.6/dist-packages (from cryptography) (1.15.1)
Requirement already satisfied: pycparser in /usr/local/lib/python3.6/dist-packages (from cffi>=1.12->cryptography) (2.21)
Installing collected packages: urllib3, charset-normalizer, requests, pyOpenSSL
Successfully installed charset-normalizer-2.0.12 pyOpenSSL-23.2.0 requests-2.27.1 urllib3-1.26.20
2025-07-09 02:52:31,421 - DEBUG - Executing command: python3 --version
2025-07-09 02:52:31,422 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 02:52:31,501 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 02:52:31,502 - DEBUG - Secsh channel 5 opened.
2025-07-09 02:52:31,521 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 02:52:31,590 - DEBUG - [chan 5] EOF received (5)
2025-07-09 02:52:31,590 - INFO - Command executed successfully: python3 --version
2025-07-09 02:52:31,591 - DEBUG - [chan 5] EOF sent (5)
2025-07-09 02:52:31,591 - DEBUG - STDOUT: Python 3.6.9
2025-07-09 02:52:31,591 - DEBUG - Executing command: python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 02:52:31,591 - DEBUG - [chan 6] Max packet in: 32768 bytes
2025-07-09 02:52:31,672 - DEBUG - [chan 6] Max packet out: 32768 bytes
2025-07-09 02:52:31,673 - DEBUG - Secsh channel 6 opened.
2025-07-09 02:52:31,697 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 02:52:31,717 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 02:52:32,874 - DEBUG - [chan 6] EOF received (6)
2025-07-09 02:52:32,875 - ERROR - Command failed with exit code 1: python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 02:52:32,876 - DEBUG - [chan 6] EOF sent (6)
2025-07-09 02:52:32,877 - ERROR - STDERR: 
2025-07-09 02:52:32,878 - INFO - [chan 0] sftp session closed.
2025-07-09 02:52:32,879 - DEBUG - [chan 0] EOF sent (0)
2025-07-09 02:52:32,880 - DEBUG - EOF in transport thread
2025-07-09 02:52:44,902 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 02:52:44,902 - DEBUG - Using password authentication
2025-07-09 02:52:44,996 - DEBUG - starting thread (client mode): 0x42d9fe30
2025-07-09 02:52:44,997 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 02:52:45,004 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 02:52:45,004 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 02:52:45,081 - DEBUG - === Key exchange possibilities ===
2025-07-09 02:52:45,082 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 02:52:45,082 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 02:52:45,083 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:52:45,083 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:52:45,083 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:52:45,084 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:52:45,084 - DEBUG - client compress: none, <EMAIL>
2025-07-09 02:52:45,085 - DEBUG - server compress: none, <EMAIL>
2025-07-09 02:52:45,085 - DEBUG - client lang: <none>
2025-07-09 02:52:45,085 - DEBUG - server lang: <none>
2025-07-09 02:52:45,086 - DEBUG - kex follows: False
2025-07-09 02:52:45,086 - DEBUG - === Key exchange agreements ===
2025-07-09 02:52:45,086 - DEBUG - Kex: <EMAIL>
2025-07-09 02:52:45,087 - DEBUG - HostKey: ssh-ed25519
2025-07-09 02:52:45,087 - DEBUG - Cipher: aes128-ctr
2025-07-09 02:52:45,087 - DEBUG - MAC: hmac-sha2-256
2025-07-09 02:52:45,087 - DEBUG - Compression: none
2025-07-09 02:52:45,087 - DEBUG - === End of kex handshake ===
2025-07-09 02:52:45,155 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 02:52:45,157 - DEBUG - Switch to new keys ...
2025-07-09 02:52:45,158 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 02:52:45,160 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 02:52:45,234 - DEBUG - userauth is OK
2025-07-09 02:52:45,256 - INFO - Authentication (password) successful!
2025-07-09 02:52:45,257 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 02:52:45,357 - DEBUG - Received global request "<EMAIL>"
2025-07-09 02:52:45,358 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 02:52:45,428 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 02:52:45,429 - DEBUG - Secsh channel 0 opened.
2025-07-09 02:52:45,451 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 02:52:45,479 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 02:52:45,479 - INFO - SSH connection established successfully
2025-07-09 02:52:45,479 - DEBUG - Executing command: echo $HOME
2025-07-09 02:52:45,479 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 02:52:45,498 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 02:52:45,498 - DEBUG - Secsh channel 1 opened.
2025-07-09 02:52:45,518 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 02:52:45,600 - DEBUG - [chan 1] EOF received (1)
2025-07-09 02:52:45,601 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 02:52:45,601 - INFO - Command executed successfully: echo $HOME
2025-07-09 02:52:45,602 - DEBUG - STDOUT: /home/<USER>
2025-07-09 02:52:45,754 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 02:52:45,773 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 02:52:46,035 - DEBUG - [chan 0] close(00000000)
2025-07-09 02:52:46,131 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 02:52:46,151 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:52:46,152 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 02:52:46,177 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 02:52:46,178 - DEBUG - Secsh channel 2 opened.
2025-07-09 02:52:46,199 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 02:52:46,248 - DEBUG - [chan 2] EOF received (2)
2025-07-09 02:52:46,248 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 02:52:46,248 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:52:46,249 - DEBUG - STDOUT: Archive:  /home/<USER>/project.zip
  inflating: /home/<USER>/dataset_generation/dataset_generation/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/project.zip  
  inflating: /home/<USER>/dataset_generation/dataset_generation/pytest.ini  
  inflating: /home/<USER>/dataset_generation/dataset_generation/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_remote_generation.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/setup.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test-requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/TESTING.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_debug.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/.gitignore  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/CACHEDIR.TAG  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/lastfailed  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/nodeids  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/analysis.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/install.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/progress.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/scenario.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/summary.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/app.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220519.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220937.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/ssh_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/topology.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen-attack-script.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_advanced_adversarial_ddos_attacks.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_icmp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_syn_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_udp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow-generic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_icmp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_syn_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_udp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/ryu_controller_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/__pycache__/ryu_controller_app.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/restart_remote_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/test_flow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/check_remote.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/conftest.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test.sh  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_detailed.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_execution.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_run.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test_detailed.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_test_with_logging.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/update_test_configs.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/test_remote_datasets.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/test_remote_datasets.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_tests.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test_simple.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_with_password.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/setup_and_run_remote.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/test_example.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/test_example.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/remote_test_utils.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/conftest.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/test_remote_datasets.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/simple_ssh_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/test_remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/generate_cicflow_dataset.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/process_pcap_to_csv.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/ssh_connect.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/main.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/test_ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/topology.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/minimal_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_all.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_basic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_env.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_remote_connection.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/ssh_test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_2.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_3.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_4.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221836.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221841.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221909.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_202736.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_220533.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221836.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221841.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221909.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/minimal_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc
2025-07-09 02:52:46,249 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 02:52:46,249 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 02:52:46,335 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 02:52:46,336 - DEBUG - Secsh channel 3 opened.
2025-07-09 02:52:46,357 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 02:52:46,420 - DEBUG - [chan 3] EOF received (3)
2025-07-09 02:52:46,421 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 02:52:46,422 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 02:52:46,422 - DEBUG - STDOUT: 
2025-07-09 02:52:46,423 - DEBUG - Executing command: python3 -m pip install --upgrade pip requests urllib3 pyOpenSSL cryptography
2025-07-09 02:52:46,424 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 02:52:46,506 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 02:52:46,507 - DEBUG - Secsh channel 4 opened.
2025-07-09 02:52:46,526 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 02:52:50,794 - DEBUG - [chan 4] EOF received (4)
2025-07-09 02:52:50,795 - INFO - Command executed successfully: python3 -m pip install --upgrade pip requests urllib3 pyOpenSSL cryptography
2025-07-09 02:52:50,795 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 02:52:50,796 - DEBUG - STDOUT: Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: pip in /usr/local/lib/python3.6/dist-packages (21.3.1)
Requirement already satisfied: requests in ./.local/lib/python3.6/site-packages (2.27.1)
Requirement already satisfied: urllib3 in ./.local/lib/python3.6/site-packages (1.26.20)
Requirement already satisfied: pyOpenSSL in ./.local/lib/python3.6/site-packages (23.2.0)
Requirement already satisfied: cryptography in /usr/local/lib/python3.6/dist-packages (40.0.2)
Requirement already satisfied: charset-normalizer~=2.0.0 in ./.local/lib/python3.6/site-packages (from requests) (2.0.12)
Requirement already satisfied: certifi>=2017.4.17 in /usr/lib/python3/dist-packages (from requests) (2018.1.18)
Requirement already satisfied: idna<4,>=2.5 in /usr/lib/python3/dist-packages (from requests) (2.6)
Requirement already satisfied: cffi>=1.12 in /usr/local/lib/python3.6/dist-packages (from cryptography) (1.15.1)
Requirement already satisfied: pycparser in /usr/local/lib/python3.6/dist-packages (from cffi>=1.12->cryptography) (2.21)
2025-07-09 02:52:50,797 - DEBUG - Executing command: python3 --version
2025-07-09 02:52:50,798 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 02:52:50,876 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 02:52:50,876 - DEBUG - Secsh channel 5 opened.
2025-07-09 02:52:50,895 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 02:52:50,960 - DEBUG - [chan 5] EOF received (5)
2025-07-09 02:52:50,960 - INFO - Command executed successfully: python3 --version
2025-07-09 02:52:50,961 - DEBUG - [chan 5] EOF sent (5)
2025-07-09 02:52:50,961 - DEBUG - STDOUT: Python 3.6.9
2025-07-09 02:52:50,961 - DEBUG - Executing command: python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 02:52:50,961 - DEBUG - [chan 6] Max packet in: 32768 bytes
2025-07-09 02:52:51,076 - DEBUG - [chan 6] Max packet out: 32768 bytes
2025-07-09 02:52:51,076 - DEBUG - Secsh channel 6 opened.
2025-07-09 02:52:51,096 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 02:52:51,116 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 02:52:52,058 - DEBUG - [chan 6] EOF received (6)
2025-07-09 02:52:52,058 - INFO - Command executed successfully: python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 02:52:52,059 - DEBUG - [chan 6] EOF sent (6)
2025-07-09 02:52:52,059 - DEBUG - STDOUT: /usr/local/lib/python3.6/dist-packages/scapy/config.py:542: CryptographyDeprecationWarning: Python 3.6 is no longer supported by the Python core team. Therefore, support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.6.

  import cryptography

2025-07-09 02:52:52,848 - ERROR - This script requires sudo privileges. Please run with sudo.
2025-07-09 02:52:52,059 - INFO - [chan 0] sftp session closed.
2025-07-09 02:52:52,059 - DEBUG - [chan 0] EOF sent (0)
2025-07-09 02:52:52,060 - DEBUG - EOF in transport thread
2025-07-09 02:53:03,183 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 02:53:03,183 - DEBUG - Using password authentication
2025-07-09 02:53:03,251 - DEBUG - starting thread (client mode): 0xcbe9fe30
2025-07-09 02:53:03,251 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 02:53:03,257 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 02:53:03,257 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 02:53:03,334 - DEBUG - === Key exchange possibilities ===
2025-07-09 02:53:03,335 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 02:53:03,335 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 02:53:03,336 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:53:03,336 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:53:03,336 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:53:03,337 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:53:03,337 - DEBUG - client compress: none, <EMAIL>
2025-07-09 02:53:03,337 - DEBUG - server compress: none, <EMAIL>
2025-07-09 02:53:03,338 - DEBUG - client lang: <none>
2025-07-09 02:53:03,338 - DEBUG - server lang: <none>
2025-07-09 02:53:03,338 - DEBUG - kex follows: False
2025-07-09 02:53:03,339 - DEBUG - === Key exchange agreements ===
2025-07-09 02:53:03,339 - DEBUG - Kex: <EMAIL>
2025-07-09 02:53:03,340 - DEBUG - HostKey: ssh-ed25519
2025-07-09 02:53:03,340 - DEBUG - Cipher: aes128-ctr
2025-07-09 02:53:03,340 - DEBUG - MAC: hmac-sha2-256
2025-07-09 02:53:03,340 - DEBUG - Compression: none
2025-07-09 02:53:03,340 - DEBUG - === End of kex handshake ===
2025-07-09 02:53:03,407 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 02:53:03,409 - DEBUG - Switch to new keys ...
2025-07-09 02:53:03,410 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 02:53:03,411 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 02:53:03,487 - DEBUG - userauth is OK
2025-07-09 02:53:03,514 - INFO - Authentication (password) successful!
2025-07-09 02:53:03,514 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 02:53:03,609 - DEBUG - Received global request "<EMAIL>"
2025-07-09 02:53:03,610 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 02:53:03,687 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 02:53:03,688 - DEBUG - Secsh channel 0 opened.
2025-07-09 02:53:03,710 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 02:53:03,731 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 02:53:03,732 - INFO - SSH connection established successfully
2025-07-09 02:53:03,732 - DEBUG - Executing command: echo $HOME
2025-07-09 02:53:03,733 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 02:53:03,753 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 02:53:03,753 - DEBUG - Secsh channel 1 opened.
2025-07-09 02:53:03,772 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 02:53:03,847 - DEBUG - [chan 1] EOF received (1)
2025-07-09 02:53:03,847 - INFO - Command executed successfully: echo $HOME
2025-07-09 02:53:03,848 - DEBUG - STDOUT: /home/<USER>
2025-07-09 02:53:03,849 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 02:53:03,967 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 02:53:03,996 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 02:53:04,261 - DEBUG - [chan 0] close(00000000)
2025-07-09 02:53:04,347 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 02:53:04,382 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:53:04,383 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 02:53:04,408 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 02:53:04,408 - DEBUG - Secsh channel 2 opened.
2025-07-09 02:53:04,429 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 02:53:04,514 - DEBUG - [chan 2] EOF received (2)
2025-07-09 02:53:04,514 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 02:53:04,514 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:53:04,515 - DEBUG - STDOUT: Archive:  /home/<USER>/project.zip
  inflating: /home/<USER>/dataset_generation/dataset_generation/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/project.zip  
  inflating: /home/<USER>/dataset_generation/dataset_generation/pytest.ini  
  inflating: /home/<USER>/dataset_generation/dataset_generation/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_remote_generation.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/setup.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test-requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/TESTING.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_debug.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/.gitignore  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/CACHEDIR.TAG  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/lastfailed  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/nodeids  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/analysis.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/install.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/progress.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/scenario.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/summary.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/app.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220519.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220937.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/ssh_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/topology.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen-attack-script.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_advanced_adversarial_ddos_attacks.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_icmp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_syn_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_udp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow-generic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_icmp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_syn_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_udp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/ryu_controller_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/__pycache__/ryu_controller_app.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/restart_remote_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/test_flow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/check_remote.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/conftest.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test.sh  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_detailed.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_execution.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_run.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test_detailed.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_test_with_logging.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/update_test_configs.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/test_remote_datasets.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/test_remote_datasets.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_tests.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test_simple.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_with_password.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/setup_and_run_remote.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/test_example.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/test_example.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/remote_test_utils.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/conftest.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/test_remote_datasets.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/simple_ssh_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/test_remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/generate_cicflow_dataset.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/process_pcap_to_csv.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/ssh_connect.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/main.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/test_ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/topology.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/minimal_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_all.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_basic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_env.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_remote_connection.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/ssh_test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_2.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_3.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_4.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221836.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221841.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221909.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_202736.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_220533.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221836.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221841.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221909.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/minimal_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc
2025-07-09 02:53:04,515 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 02:53:04,515 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 02:53:04,598 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 02:53:04,598 - DEBUG - Secsh channel 3 opened.
2025-07-09 02:53:04,619 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 02:53:04,693 - DEBUG - [chan 3] EOF received (3)
2025-07-09 02:53:04,693 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 02:53:04,693 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 02:53:04,694 - DEBUG - STDOUT: 
2025-07-09 02:53:04,694 - DEBUG - Executing command: python3 -m pip install --upgrade pip requests urllib3 pyOpenSSL cryptography
2025-07-09 02:53:04,694 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 02:53:04,773 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 02:53:04,774 - DEBUG - Secsh channel 4 opened.
2025-07-09 02:53:04,794 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 02:53:09,079 - DEBUG - [chan 4] EOF received (4)
2025-07-09 02:53:09,079 - INFO - Command executed successfully: python3 -m pip install --upgrade pip requests urllib3 pyOpenSSL cryptography
2025-07-09 02:53:09,079 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 02:53:09,079 - DEBUG - STDOUT: Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: pip in /usr/local/lib/python3.6/dist-packages (21.3.1)
Requirement already satisfied: requests in ./.local/lib/python3.6/site-packages (2.27.1)
Requirement already satisfied: urllib3 in ./.local/lib/python3.6/site-packages (1.26.20)
Requirement already satisfied: pyOpenSSL in ./.local/lib/python3.6/site-packages (23.2.0)
Requirement already satisfied: cryptography in /usr/local/lib/python3.6/dist-packages (40.0.2)
Requirement already satisfied: charset-normalizer~=2.0.0 in ./.local/lib/python3.6/site-packages (from requests) (2.0.12)
Requirement already satisfied: idna<4,>=2.5 in /usr/lib/python3/dist-packages (from requests) (2.6)
Requirement already satisfied: certifi>=2017.4.17 in /usr/lib/python3/dist-packages (from requests) (2018.1.18)
Requirement already satisfied: cffi>=1.12 in /usr/local/lib/python3.6/dist-packages (from cryptography) (1.15.1)
Requirement already satisfied: pycparser in /usr/local/lib/python3.6/dist-packages (from cffi>=1.12->cryptography) (2.21)
2025-07-09 02:53:09,080 - DEBUG - Executing command: python3 --version
2025-07-09 02:53:09,080 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 02:53:09,159 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 02:53:09,159 - DEBUG - Secsh channel 5 opened.
2025-07-09 02:53:09,179 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 02:53:09,245 - DEBUG - [chan 5] EOF received (5)
2025-07-09 02:53:09,246 - INFO - Command executed successfully: python3 --version
2025-07-09 02:53:09,246 - DEBUG - STDOUT: Python 3.6.9
2025-07-09 02:53:09,246 - DEBUG - [chan 5] EOF sent (5)
2025-07-09 02:53:09,246 - DEBUG - Executing command: sudo python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 02:53:09,246 - DEBUG - [chan 6] Max packet in: 32768 bytes
2025-07-09 02:53:09,327 - DEBUG - [chan 6] Max packet out: 32768 bytes
2025-07-09 02:53:09,327 - DEBUG - Secsh channel 6 opened.
2025-07-09 02:53:09,349 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 02:53:09,371 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 02:57:25,408 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 02:57:25,408 - DEBUG - Using password authentication
2025-07-09 02:57:25,486 - DEBUG - starting thread (client mode): 0x84b2fe30
2025-07-09 02:57:25,486 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 02:57:25,486 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 02:57:25,487 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 02:57:25,555 - DEBUG - === Key exchange possibilities ===
2025-07-09 02:57:25,556 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 02:57:25,556 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 02:57:25,557 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:57:25,557 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:57:25,558 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:57:25,558 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:57:25,558 - DEBUG - client compress: none, <EMAIL>
2025-07-09 02:57:25,559 - DEBUG - server compress: none, <EMAIL>
2025-07-09 02:57:25,559 - DEBUG - client lang: <none>
2025-07-09 02:57:25,560 - DEBUG - server lang: <none>
2025-07-09 02:57:25,560 - DEBUG - kex follows: False
2025-07-09 02:57:25,560 - DEBUG - === Key exchange agreements ===
2025-07-09 02:57:25,561 - DEBUG - Kex: <EMAIL>
2025-07-09 02:57:25,561 - DEBUG - HostKey: ssh-ed25519
2025-07-09 02:57:25,562 - DEBUG - Cipher: aes128-ctr
2025-07-09 02:57:25,562 - DEBUG - MAC: hmac-sha2-256
2025-07-09 02:57:25,563 - DEBUG - Compression: none
2025-07-09 02:57:25,563 - DEBUG - === End of kex handshake ===
2025-07-09 02:57:25,642 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 02:57:25,645 - DEBUG - Switch to new keys ...
2025-07-09 02:57:25,648 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 02:57:25,649 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 02:57:25,732 - DEBUG - userauth is OK
2025-07-09 02:57:25,755 - INFO - Authentication (password) successful!
2025-07-09 02:57:25,756 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 02:57:25,851 - DEBUG - Received global request "<EMAIL>"
2025-07-09 02:57:25,852 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 02:57:25,914 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 02:57:25,914 - DEBUG - Secsh channel 0 opened.
2025-07-09 02:57:25,934 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 02:57:25,954 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 02:57:25,954 - INFO - SSH connection established successfully
2025-07-09 02:57:25,954 - DEBUG - Executing command: echo $HOME
2025-07-09 02:57:25,954 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 02:57:25,975 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 02:57:25,976 - DEBUG - Secsh channel 1 opened.
2025-07-09 02:57:25,997 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 02:57:26,058 - DEBUG - [chan 1] EOF received (1)
2025-07-09 02:57:26,058 - INFO - Command executed successfully: echo $HOME
2025-07-09 02:57:26,059 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 02:57:26,059 - DEBUG - STDOUT: /home/<USER>
2025-07-09 02:57:26,176 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 02:57:26,221 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 02:57:26,558 - DEBUG - [chan 0] close(00000000)
2025-07-09 02:57:26,628 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 02:57:26,651 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:57:26,652 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 02:57:26,672 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 02:57:26,672 - DEBUG - Secsh channel 2 opened.
2025-07-09 02:57:26,693 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 02:57:26,760 - DEBUG - [chan 2] EOF received (2)
2025-07-09 02:57:26,761 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 02:57:26,761 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:57:26,761 - DEBUG - STDOUT: Archive:  /home/<USER>/project.zip
  inflating: /home/<USER>/dataset_generation/dataset_generation/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/project.zip  
  inflating: /home/<USER>/dataset_generation/dataset_generation/pytest.ini  
  inflating: /home/<USER>/dataset_generation/dataset_generation/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_remote_generation.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/setup.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test-requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/TESTING.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_debug.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/.gitignore  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/CACHEDIR.TAG  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/lastfailed  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/nodeids  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/analysis.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/install.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/progress.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/scenario.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/summary.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/app.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220519.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220937.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/ssh_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/topology.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen-attack-script.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_advanced_adversarial_ddos_attacks.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_icmp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_syn_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_udp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow-generic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_icmp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_syn_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_udp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/ryu_controller_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/__pycache__/ryu_controller_app.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/restart_remote_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/test_flow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/check_remote.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/conftest.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test.sh  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_detailed.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_execution.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_run.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test_detailed.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_test_with_logging.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/update_test_configs.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/test_remote_datasets.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/test_remote_datasets.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_tests.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test_simple.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_with_password.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/setup_and_run_remote.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/test_example.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/test_example.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/remote_test_utils.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/conftest.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/test_remote_datasets.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/simple_ssh_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/test_remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/generate_cicflow_dataset.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/process_pcap_to_csv.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/ssh_connect.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/main.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/test_ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/topology.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/minimal_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_all.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_basic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_env.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_remote_connection.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/ssh_test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_2.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_3.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_4.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221836.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221841.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221909.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_202736.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_220533.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221836.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221841.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221909.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/minimal_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc
2025-07-09 02:57:26,761 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 02:57:26,762 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 02:57:26,844 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 02:57:26,846 - DEBUG - Secsh channel 3 opened.
2025-07-09 02:57:26,875 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 02:57:26,938 - DEBUG - [chan 3] EOF received (3)
2025-07-09 02:57:26,939 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 02:57:26,939 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 02:57:26,939 - DEBUG - STDOUT: 
2025-07-09 02:57:26,939 - DEBUG - Executing command: python3 -m pip install --upgrade pip requests urllib3 pyOpenSSL cryptography
2025-07-09 02:57:26,939 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 02:57:27,022 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 02:57:27,022 - DEBUG - Secsh channel 4 opened.
2025-07-09 02:57:27,043 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 02:57:31,317 - DEBUG - [chan 4] EOF received (4)
2025-07-09 02:57:31,318 - INFO - Command executed successfully: python3 -m pip install --upgrade pip requests urllib3 pyOpenSSL cryptography
2025-07-09 02:57:31,318 - DEBUG - STDOUT: Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: pip in /usr/local/lib/python3.6/dist-packages (21.3.1)
Requirement already satisfied: requests in ./.local/lib/python3.6/site-packages (2.27.1)
Requirement already satisfied: urllib3 in ./.local/lib/python3.6/site-packages (1.26.20)
Requirement already satisfied: pyOpenSSL in ./.local/lib/python3.6/site-packages (23.2.0)
Requirement already satisfied: cryptography in /usr/local/lib/python3.6/dist-packages (40.0.2)
Requirement already satisfied: charset-normalizer~=2.0.0 in ./.local/lib/python3.6/site-packages (from requests) (2.0.12)
Requirement already satisfied: certifi>=2017.4.17 in /usr/lib/python3/dist-packages (from requests) (2018.1.18)
Requirement already satisfied: idna<4,>=2.5 in /usr/lib/python3/dist-packages (from requests) (2.6)
Requirement already satisfied: cffi>=1.12 in /usr/local/lib/python3.6/dist-packages (from cryptography) (1.15.1)
Requirement already satisfied: pycparser in /usr/local/lib/python3.6/dist-packages (from cffi>=1.12->cryptography) (2.21)
2025-07-09 02:57:31,318 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 02:57:31,318 - DEBUG - Executing command: python3 --version
2025-07-09 02:57:31,318 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 02:57:31,405 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 02:57:31,405 - DEBUG - Secsh channel 5 opened.
2025-07-09 02:57:31,425 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 02:57:31,496 - DEBUG - [chan 5] EOF received (5)
2025-07-09 02:57:31,496 - INFO - Command executed successfully: python3 --version
2025-07-09 02:57:31,497 - DEBUG - STDOUT: Python 3.6.9
2025-07-09 02:57:31,497 - DEBUG - [chan 5] EOF sent (5)
2025-07-09 02:57:31,497 - DEBUG - Executing command: sudo python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 02:57:31,498 - DEBUG - [chan 6] Max packet in: 32768 bytes
2025-07-09 02:57:31,585 - DEBUG - [chan 6] Max packet out: 32768 bytes
2025-07-09 02:57:31,586 - DEBUG - Secsh channel 6 opened.
2025-07-09 02:57:31,607 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 02:57:31,628 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 02:58:53,881 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 02:58:53,882 - DEBUG - Using password authentication
2025-07-09 02:58:53,961 - DEBUG - starting thread (client mode): 0x9208fe30
2025-07-09 02:58:53,962 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 02:58:53,962 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 02:58:53,962 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 02:58:53,982 - DEBUG - === Key exchange possibilities ===
2025-07-09 02:58:53,982 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 02:58:53,983 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 02:58:53,983 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:58:53,984 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:58:53,984 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:58:53,985 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:58:53,985 - DEBUG - client compress: none, <EMAIL>
2025-07-09 02:58:53,985 - DEBUG - server compress: none, <EMAIL>
2025-07-09 02:58:53,986 - DEBUG - client lang: <none>
2025-07-09 02:58:53,986 - DEBUG - server lang: <none>
2025-07-09 02:58:53,986 - DEBUG - kex follows: False
2025-07-09 02:58:53,987 - DEBUG - === Key exchange agreements ===
2025-07-09 02:58:53,987 - DEBUG - Kex: <EMAIL>
2025-07-09 02:58:53,988 - DEBUG - HostKey: ssh-ed25519
2025-07-09 02:58:53,988 - DEBUG - Cipher: aes128-ctr
2025-07-09 02:58:53,988 - DEBUG - MAC: hmac-sha2-256
2025-07-09 02:58:53,989 - DEBUG - Compression: none
2025-07-09 02:58:53,989 - DEBUG - === End of kex handshake ===
2025-07-09 02:58:54,072 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 02:58:54,073 - DEBUG - Switch to new keys ...
2025-07-09 02:58:54,073 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 02:58:54,073 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 02:58:54,153 - DEBUG - userauth is OK
2025-07-09 02:58:54,203 - INFO - Authentication (password) successful!
2025-07-09 02:58:54,204 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 02:58:54,276 - DEBUG - Received global request "<EMAIL>"
2025-07-09 02:58:54,276 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 02:58:54,346 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 02:58:54,347 - DEBUG - Secsh channel 0 opened.
2025-07-09 02:58:54,368 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 02:58:54,388 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 02:58:54,389 - INFO - SSH connection established successfully
2025-07-09 02:58:54,390 - DEBUG - Executing command: echo $HOME
2025-07-09 02:58:54,391 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 02:58:54,411 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 02:58:54,411 - DEBUG - Secsh channel 1 opened.
2025-07-09 02:58:54,431 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 02:58:54,512 - DEBUG - [chan 1] EOF received (1)
2025-07-09 02:58:54,513 - INFO - Command executed successfully: echo $HOME
2025-07-09 02:58:54,514 - DEBUG - STDOUT: /home/<USER>
2025-07-09 02:58:54,514 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 02:58:54,645 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 02:58:54,665 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 02:58:54,939 - DEBUG - [chan 0] close(00000000)
2025-07-09 02:58:54,999 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 02:58:55,021 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:58:55,021 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 02:58:55,041 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 02:58:55,041 - DEBUG - Secsh channel 2 opened.
2025-07-09 02:58:55,061 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 02:58:55,109 - DEBUG - [chan 2] EOF received (2)
2025-07-09 02:58:55,109 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 02:58:55,109 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 02:58:55,109 - DEBUG - STDOUT: Archive:  /home/<USER>/project.zip
  inflating: /home/<USER>/dataset_generation/dataset_generation/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/project.zip  
  inflating: /home/<USER>/dataset_generation/dataset_generation/pytest.ini  
  inflating: /home/<USER>/dataset_generation/dataset_generation/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_remote_generation.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/setup.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test-requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/TESTING.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_debug.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/.gitignore  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/CACHEDIR.TAG  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/lastfailed  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/nodeids  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/analysis.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/install.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/progress.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/scenario.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/summary.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/app.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220519.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220937.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/ssh_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/topology.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen-attack-script.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_advanced_adversarial_ddos_attacks.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_icmp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_syn_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_udp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow-generic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_icmp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_syn_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_udp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/ryu_controller_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/__pycache__/ryu_controller_app.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/restart_remote_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/test_flow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/check_remote.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/conftest.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test.sh  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_detailed.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_execution.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_run.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test_detailed.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_test_with_logging.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/update_test_configs.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/test_remote_datasets.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/test_remote_datasets.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_tests.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test_simple.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_with_password.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/setup_and_run_remote.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/test_example.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/test_example.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/remote_test_utils.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/conftest.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/test_remote_datasets.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/simple_ssh_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/test_remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/generate_cicflow_dataset.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/process_pcap_to_csv.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/ssh_connect.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/main.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/test_ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/topology.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/minimal_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_all.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_basic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_env.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_remote_connection.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/ssh_test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_2.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_3.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_4.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221836.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221841.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221909.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_202736.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_220533.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221836.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221841.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221909.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/minimal_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc
2025-07-09 02:58:55,110 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 02:58:55,110 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 02:58:55,193 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 02:58:55,194 - DEBUG - Secsh channel 3 opened.
2025-07-09 02:58:55,213 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 02:58:55,276 - DEBUG - [chan 3] EOF received (3)
2025-07-09 02:58:55,278 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 02:58:55,278 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 02:58:55,278 - DEBUG - STDOUT: 
2025-07-09 02:58:55,278 - DEBUG - Executing command: python3 -m pip install --upgrade pip requests urllib3 pyOpenSSL cryptography
2025-07-09 02:58:55,279 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 02:58:55,364 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 02:58:55,364 - DEBUG - Secsh channel 4 opened.
2025-07-09 02:58:55,387 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 02:58:59,649 - DEBUG - [chan 4] EOF received (4)
2025-07-09 02:58:59,649 - INFO - Command executed successfully: python3 -m pip install --upgrade pip requests urllib3 pyOpenSSL cryptography
2025-07-09 02:58:59,649 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 02:58:59,650 - DEBUG - STDOUT: Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: pip in /usr/local/lib/python3.6/dist-packages (21.3.1)
Requirement already satisfied: requests in ./.local/lib/python3.6/site-packages (2.27.1)
Requirement already satisfied: urllib3 in ./.local/lib/python3.6/site-packages (1.26.20)
Requirement already satisfied: pyOpenSSL in ./.local/lib/python3.6/site-packages (23.2.0)
Requirement already satisfied: cryptography in /usr/local/lib/python3.6/dist-packages (40.0.2)
Requirement already satisfied: idna<4,>=2.5 in /usr/lib/python3/dist-packages (from requests) (2.6)
Requirement already satisfied: certifi>=2017.4.17 in /usr/lib/python3/dist-packages (from requests) (2018.1.18)
Requirement already satisfied: charset-normalizer~=2.0.0 in ./.local/lib/python3.6/site-packages (from requests) (2.0.12)
Requirement already satisfied: cffi>=1.12 in /usr/local/lib/python3.6/dist-packages (from cryptography) (1.15.1)
Requirement already satisfied: pycparser in /usr/local/lib/python3.6/dist-packages (from cffi>=1.12->cryptography) (2.21)
2025-07-09 02:58:59,650 - DEBUG - Executing command: python3 --version
2025-07-09 02:58:59,650 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 02:58:59,733 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 02:58:59,733 - DEBUG - Secsh channel 5 opened.
2025-07-09 02:58:59,753 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 02:58:59,819 - DEBUG - [chan 5] EOF received (5)
2025-07-09 02:58:59,819 - DEBUG - [chan 5] EOF sent (5)
2025-07-09 02:58:59,819 - INFO - Command executed successfully: python3 --version
2025-07-09 02:58:59,820 - DEBUG - STDOUT: Python 3.6.9
2025-07-09 02:58:59,820 - DEBUG - Executing command: sudo python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 02:58:59,820 - DEBUG - [chan 6] Max packet in: 32768 bytes
2025-07-09 02:58:59,906 - DEBUG - [chan 6] Max packet out: 32768 bytes
2025-07-09 02:58:59,907 - DEBUG - Secsh channel 6 opened.
2025-07-09 02:58:59,928 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 02:58:59,958 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 03:00:36,716 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 03:00:36,716 - DEBUG - Using password authentication
2025-07-09 03:00:39,471 - DEBUG - starting thread (client mode): 0x3b82fe30
2025-07-09 03:00:39,472 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 03:00:39,496 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 03:00:39,496 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 03:00:39,520 - DEBUG - === Key exchange possibilities ===
2025-07-09 03:00:39,520 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 03:00:39,520 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 03:00:39,520 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:00:39,520 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:00:39,520 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:00:39,520 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:00:39,520 - DEBUG - client compress: none, <EMAIL>
2025-07-09 03:00:39,520 - DEBUG - server compress: none, <EMAIL>
2025-07-09 03:00:39,520 - DEBUG - client lang: <none>
2025-07-09 03:00:39,520 - DEBUG - server lang: <none>
2025-07-09 03:00:39,520 - DEBUG - kex follows: False
2025-07-09 03:00:39,521 - DEBUG - === Key exchange agreements ===
2025-07-09 03:00:39,521 - DEBUG - Kex: <EMAIL>
2025-07-09 03:00:39,521 - DEBUG - HostKey: ssh-ed25519
2025-07-09 03:00:39,521 - DEBUG - Cipher: aes128-ctr
2025-07-09 03:00:39,521 - DEBUG - MAC: hmac-sha2-256
2025-07-09 03:00:39,521 - DEBUG - Compression: none
2025-07-09 03:00:39,521 - DEBUG - === End of kex handshake ===
2025-07-09 03:00:39,546 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 03:00:39,547 - DEBUG - Switch to new keys ...
2025-07-09 03:00:39,547 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 03:00:39,548 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 03:00:39,630 - DEBUG - userauth is OK
2025-07-09 03:00:39,652 - INFO - Authentication (password) successful!
2025-07-09 03:00:39,652 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 03:00:39,749 - DEBUG - Received global request "<EMAIL>"
2025-07-09 03:00:39,750 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 03:00:39,818 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 03:00:39,818 - DEBUG - Secsh channel 0 opened.
2025-07-09 03:00:39,839 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 03:00:39,865 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 03:00:39,865 - INFO - SSH connection established successfully
2025-07-09 03:00:39,865 - DEBUG - Executing command: echo $HOME
2025-07-09 03:00:39,865 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 03:00:39,885 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 03:00:39,885 - DEBUG - Secsh channel 1 opened.
2025-07-09 03:00:39,904 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 03:00:39,983 - DEBUG - [chan 1] EOF received (1)
2025-07-09 03:00:39,984 - INFO - Command executed successfully: echo $HOME
2025-07-09 03:00:39,985 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 03:00:39,985 - DEBUG - STDOUT: /home/<USER>
2025-07-09 03:00:40,102 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 03:00:40,129 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 03:00:40,499 - DEBUG - [chan 0] close(00000000)
2025-07-09 03:00:40,556 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 03:00:40,576 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:00:40,576 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 03:00:40,596 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 03:00:40,596 - DEBUG - Secsh channel 2 opened.
2025-07-09 03:00:40,615 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 03:00:40,664 - DEBUG - [chan 2] EOF received (2)
2025-07-09 03:00:40,664 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 03:00:40,664 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:00:40,664 - DEBUG - STDOUT: Archive:  /home/<USER>/project.zip
  inflating: /home/<USER>/dataset_generation/dataset_generation/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/project.zip  
  inflating: /home/<USER>/dataset_generation/dataset_generation/pytest.ini  
  inflating: /home/<USER>/dataset_generation/dataset_generation/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_remote_generation.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/setup.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test-requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/TESTING.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_debug.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/.gitignore  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/CACHEDIR.TAG  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/lastfailed  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/nodeids  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/analysis.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/install.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/progress.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/scenario.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/summary.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/app.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220519.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220937.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/ssh_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/topology.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen-attack-script.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_advanced_adversarial_ddos_attacks.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_icmp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_syn_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_udp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow-generic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_icmp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_syn_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_udp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/ryu_controller_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/__pycache__/ryu_controller_app.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/restart_remote_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/test_flow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/check_remote.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/conftest.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test.sh  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_detailed.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_execution.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_run.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test_detailed.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_test_with_logging.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/update_test_configs.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/test_remote_datasets.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/test_remote_datasets.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_tests.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test_simple.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_with_password.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/setup_and_run_remote.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/test_example.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/test_example.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/remote_test_utils.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/conftest.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/test_remote_datasets.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/simple_ssh_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/test_remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/generate_cicflow_dataset.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/process_pcap_to_csv.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/ssh_connect.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/main.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/test_ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/topology.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/minimal_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_all.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_basic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_env.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_remote_connection.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/ssh_test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_2.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_3.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_4.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221836.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221841.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221909.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_202736.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_220533.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221836.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221841.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221909.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/minimal_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc
2025-07-09 03:00:40,665 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 03:00:40,665 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 03:00:40,744 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 03:00:40,744 - DEBUG - Secsh channel 3 opened.
2025-07-09 03:00:40,768 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 03:00:40,843 - DEBUG - [chan 3] EOF received (3)
2025-07-09 03:00:40,844 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 03:00:40,845 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 03:00:40,845 - DEBUG - STDOUT: 
2025-07-09 03:00:40,846 - DEBUG - Executing command: python3 -m pip install --upgrade pip requests urllib3 pyOpenSSL cryptography
2025-07-09 03:00:40,847 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 03:00:40,934 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 03:00:40,935 - DEBUG - Secsh channel 4 opened.
2025-07-09 03:00:40,962 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 03:00:45,236 - DEBUG - [chan 4] EOF received (4)
2025-07-09 03:00:45,237 - INFO - Command executed successfully: python3 -m pip install --upgrade pip requests urllib3 pyOpenSSL cryptography
2025-07-09 03:00:45,238 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 03:00:45,238 - DEBUG - STDOUT: Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: pip in /usr/local/lib/python3.6/dist-packages (21.3.1)
Requirement already satisfied: requests in ./.local/lib/python3.6/site-packages (2.27.1)
Requirement already satisfied: urllib3 in ./.local/lib/python3.6/site-packages (1.26.20)
Requirement already satisfied: pyOpenSSL in ./.local/lib/python3.6/site-packages (23.2.0)
Requirement already satisfied: cryptography in /usr/local/lib/python3.6/dist-packages (40.0.2)
Requirement already satisfied: certifi>=2017.4.17 in /usr/lib/python3/dist-packages (from requests) (2018.1.18)
Requirement already satisfied: idna<4,>=2.5 in /usr/lib/python3/dist-packages (from requests) (2.6)
Requirement already satisfied: charset-normalizer~=2.0.0 in ./.local/lib/python3.6/site-packages (from requests) (2.0.12)
Requirement already satisfied: cffi>=1.12 in /usr/local/lib/python3.6/dist-packages (from cryptography) (1.15.1)
Requirement already satisfied: pycparser in /usr/local/lib/python3.6/dist-packages (from cffi>=1.12->cryptography) (2.21)
2025-07-09 03:00:45,240 - DEBUG - Executing command: python3 --version
2025-07-09 03:00:45,240 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 03:00:45,325 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 03:00:45,326 - DEBUG - Secsh channel 5 opened.
2025-07-09 03:00:45,346 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 03:00:45,416 - DEBUG - [chan 5] EOF received (5)
2025-07-09 03:00:45,416 - INFO - Command executed successfully: python3 --version
2025-07-09 03:00:45,416 - DEBUG - STDOUT: Python 3.6.9
2025-07-09 03:00:45,416 - DEBUG - [chan 5] EOF sent (5)
2025-07-09 03:00:45,416 - DEBUG - Executing command: sudo python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 03:00:45,416 - DEBUG - [chan 6] Max packet in: 32768 bytes
2025-07-09 03:00:45,511 - DEBUG - [chan 6] Max packet out: 32768 bytes
2025-07-09 03:00:45,511 - DEBUG - Secsh channel 6 opened.
2025-07-09 03:00:45,531 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 03:00:45,551 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 03:01:27,647 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 03:01:27,647 - DEBUG - Using password authentication
2025-07-09 03:01:28,143 - DEBUG - starting thread (client mode): 0x3d843170
2025-07-09 03:01:28,144 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 03:01:28,169 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 03:01:28,169 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 03:01:28,191 - DEBUG - === Key exchange possibilities ===
2025-07-09 03:01:28,191 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 03:01:28,192 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 03:01:28,192 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:01:28,193 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:01:28,193 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:01:28,193 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:01:28,194 - DEBUG - client compress: none, <EMAIL>
2025-07-09 03:01:28,194 - DEBUG - server compress: none, <EMAIL>
2025-07-09 03:01:28,194 - DEBUG - client lang: <none>
2025-07-09 03:01:28,195 - DEBUG - server lang: <none>
2025-07-09 03:01:28,195 - DEBUG - kex follows: False
2025-07-09 03:01:28,195 - DEBUG - === Key exchange agreements ===
2025-07-09 03:01:28,196 - DEBUG - Kex: <EMAIL>
2025-07-09 03:01:28,196 - DEBUG - HostKey: ssh-ed25519
2025-07-09 03:01:28,197 - DEBUG - Cipher: aes128-ctr
2025-07-09 03:01:28,197 - DEBUG - MAC: hmac-sha2-256
2025-07-09 03:01:28,197 - DEBUG - Compression: none
2025-07-09 03:01:28,197 - DEBUG - === End of kex handshake ===
2025-07-09 03:01:28,222 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 03:01:28,223 - DEBUG - Switch to new keys ...
2025-07-09 03:01:28,223 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 03:01:28,224 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 03:01:28,314 - DEBUG - userauth is OK
2025-07-09 03:01:28,336 - INFO - Authentication (password) successful!
2025-07-09 03:01:28,337 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 03:01:28,430 - DEBUG - Received global request "<EMAIL>"
2025-07-09 03:01:28,430 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 03:01:28,490 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 03:01:28,490 - DEBUG - Secsh channel 0 opened.
2025-07-09 03:01:28,510 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 03:01:28,537 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 03:01:28,538 - INFO - SSH connection established successfully
2025-07-09 03:01:28,538 - DEBUG - Executing command: echo $HOME
2025-07-09 03:01:28,538 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 03:01:28,559 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 03:01:28,559 - DEBUG - Secsh channel 1 opened.
2025-07-09 03:01:28,579 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 03:01:28,650 - DEBUG - [chan 1] EOF received (1)
2025-07-09 03:01:28,650 - INFO - Command executed successfully: echo $HOME
2025-07-09 03:01:28,650 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 03:01:28,650 - DEBUG - STDOUT: /home/<USER>
2025-07-09 03:01:28,766 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 03:01:28,798 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 03:01:29,073 - DEBUG - [chan 0] close(00000000)
2025-07-09 03:01:29,134 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 03:01:29,156 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:01:29,157 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 03:01:29,184 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 03:01:29,184 - DEBUG - Secsh channel 2 opened.
2025-07-09 03:01:29,242 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 03:01:29,286 - DEBUG - [chan 2] EOF received (2)
2025-07-09 03:01:29,286 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:01:29,286 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 03:01:29,286 - DEBUG - STDOUT: Archive:  /home/<USER>/project.zip
  inflating: /home/<USER>/dataset_generation/dataset_generation/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/project.zip  
  inflating: /home/<USER>/dataset_generation/dataset_generation/pytest.ini  
  inflating: /home/<USER>/dataset_generation/dataset_generation/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_remote_generation.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/setup.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test-requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/TESTING.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_debug.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/.gitignore  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/CACHEDIR.TAG  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/lastfailed  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/nodeids  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/analysis.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/install.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/progress.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/scenario.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/summary.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/app.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220519.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220937.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/ssh_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/topology.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen-attack-script.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_advanced_adversarial_ddos_attacks.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_icmp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_syn_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_udp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow-generic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_icmp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_syn_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_udp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/ryu_controller_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/__pycache__/ryu_controller_app.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/restart_remote_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/test_flow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/check_remote.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/conftest.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test.sh  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_detailed.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_execution.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_run.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test_detailed.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_test_with_logging.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/update_test_configs.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/test_remote_datasets.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/test_remote_datasets.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_tests.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test_simple.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_with_password.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/setup_and_run_remote.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/test_example.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/test_example.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/remote_test_utils.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/conftest.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/test_remote_datasets.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/simple_ssh_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/test_remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/generate_cicflow_dataset.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/process_pcap_to_csv.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/ssh_connect.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/main.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/test_ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/topology.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/minimal_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_all.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_basic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_env.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_remote_connection.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/ssh_test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_2.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_3.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_4.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221836.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221841.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221909.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_202736.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_220533.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221836.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221841.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221909.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/minimal_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc
2025-07-09 03:01:29,287 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 03:01:29,287 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 03:01:29,368 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 03:01:29,369 - DEBUG - Secsh channel 3 opened.
2025-07-09 03:01:29,389 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 03:01:29,460 - DEBUG - [chan 3] EOF received (3)
2025-07-09 03:01:29,461 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 03:01:29,461 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 03:01:29,462 - DEBUG - STDOUT: 
2025-07-09 03:01:29,463 - DEBUG - Executing command: python3 --version
2025-07-09 03:01:29,464 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 03:01:29,547 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 03:01:29,547 - DEBUG - Secsh channel 4 opened.
2025-07-09 03:01:29,566 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 03:01:29,646 - DEBUG - [chan 4] EOF received (4)
2025-07-09 03:01:29,647 - INFO - Command executed successfully: python3 --version
2025-07-09 03:01:29,647 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 03:01:29,647 - DEBUG - STDOUT: Python 3.6.9
2025-07-09 03:01:29,647 - DEBUG - Executing command: sudo python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 03:01:29,648 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 03:01:29,729 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 03:01:29,729 - DEBUG - Secsh channel 5 opened.
2025-07-09 03:01:29,749 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 03:01:29,769 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 03:02:31,070 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 03:02:31,070 - DEBUG - Using password authentication
2025-07-09 03:02:31,151 - DEBUG - starting thread (client mode): 0x89893170
2025-07-09 03:02:31,151 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 03:02:31,177 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 03:02:31,177 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 03:02:31,209 - DEBUG - === Key exchange possibilities ===
2025-07-09 03:02:31,209 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 03:02:31,210 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 03:02:31,210 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:02:31,211 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:02:31,211 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:02:31,211 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:02:31,212 - DEBUG - client compress: none, <EMAIL>
2025-07-09 03:02:31,212 - DEBUG - server compress: none, <EMAIL>
2025-07-09 03:02:31,212 - DEBUG - client lang: <none>
2025-07-09 03:02:31,213 - DEBUG - server lang: <none>
2025-07-09 03:02:31,213 - DEBUG - kex follows: False
2025-07-09 03:02:31,213 - DEBUG - === Key exchange agreements ===
2025-07-09 03:02:31,214 - DEBUG - Kex: <EMAIL>
2025-07-09 03:02:31,214 - DEBUG - HostKey: ssh-ed25519
2025-07-09 03:02:31,215 - DEBUG - Cipher: aes128-ctr
2025-07-09 03:02:31,215 - DEBUG - MAC: hmac-sha2-256
2025-07-09 03:02:31,215 - DEBUG - Compression: none
2025-07-09 03:02:31,216 - DEBUG - === End of kex handshake ===
2025-07-09 03:02:31,249 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 03:02:31,251 - DEBUG - Switch to new keys ...
2025-07-09 03:02:31,252 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 03:02:31,253 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 03:02:31,332 - DEBUG - userauth is OK
2025-07-09 03:02:31,354 - INFO - Authentication (password) successful!
2025-07-09 03:02:31,354 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 03:02:31,451 - DEBUG - Received global request "<EMAIL>"
2025-07-09 03:02:31,452 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 03:02:31,517 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 03:02:31,517 - DEBUG - Secsh channel 0 opened.
2025-07-09 03:02:31,550 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 03:02:31,570 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 03:02:31,570 - INFO - SSH connection established successfully
2025-07-09 03:02:31,570 - DEBUG - Executing command: echo $HOME
2025-07-09 03:02:31,571 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 03:02:31,597 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 03:02:31,598 - DEBUG - Secsh channel 1 opened.
2025-07-09 03:02:31,620 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 03:02:31,702 - DEBUG - [chan 1] EOF received (1)
2025-07-09 03:02:31,703 - INFO - Command executed successfully: echo $HOME
2025-07-09 03:02:31,704 - DEBUG - STDOUT: /home/<USER>
2025-07-09 03:02:31,704 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 03:02:31,834 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 03:02:31,868 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 03:02:32,194 - DEBUG - [chan 0] close(00000000)
2025-07-09 03:02:32,286 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 03:02:32,315 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:02:32,316 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 03:02:32,337 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 03:02:32,338 - DEBUG - Secsh channel 2 opened.
2025-07-09 03:02:32,360 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 03:02:32,486 - DEBUG - [chan 2] EOF received (2)
2025-07-09 03:02:32,486 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:02:32,486 - DEBUG - STDOUT: Archive:  /home/<USER>/project.zip
  inflating: /home/<USER>/dataset_generation/dataset_generation/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/main_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/project.zip  
  inflating: /home/<USER>/dataset_generation/dataset_generation/pytest.ini  
  inflating: /home/<USER>/dataset_generation/dataset_generation/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_remote_generation.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/run_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/setup.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test-requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/TESTING.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_debug.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/.gitignore  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/CACHEDIR.TAG  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/lastfailed  
  inflating: /home/<USER>/dataset_generation/dataset_generation/.pytest_cache/v/cache/nodeids  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/requirements.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/config/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/analysis.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/install.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/progress.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/README.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/scenario.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/docs/summary.md  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/app.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220519.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/dataset_test_20250708_220937.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/ssh_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote_exec.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/topology.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen-attack-script.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_advanced_adversarial_ddos_attacks.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_icmp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_syn_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/gen_udp_flood.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow-generic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/sdn-dataset-workflow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_icmp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_syn_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/attacks/__pycache__/gen_udp_flood.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/ryu_controller_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/controller/__pycache__/ryu_controller_app.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/remote/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/restart_remote_app.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/scripts/test_flow.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/check_remote.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/conftest.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test.sh  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_detailed.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_execution.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/remote_test_run.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_remote_test_detailed.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/run_test_with_logging.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/test_config.json  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/update_test_configs.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/test_remote_datasets.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/test_remote_datasets.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/e2e/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/integration/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_tests.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_test_simple.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/run_remote_with_password.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/scripts/setup_and_run_remote.ps1  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/test_example.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/test_example.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/unit/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/remote_test_utils.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/utils/__init__.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/conftest.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/simple_test.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/test_remote_datasets.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/simple_ssh_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/test_remote_exec.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/tests/integration/__pycache__/test_remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/generate_cicflow_dataset.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/process_pcap_to_csv.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/utils/ssh_connect.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/main.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/remote_exec.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/test_ssh_connect.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/topology.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/src/__pycache__/__init__.cpython-312.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/minimal_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/simple_test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_all.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_basic.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_env.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_remote_connection.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/tests/test_ssh_simple.py  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_logs/test.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/ssh_test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_2.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_3.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_integration_output_4.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_output.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221836.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221841.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_report_20250708_221909.txt  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_202736.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_220533.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221836.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221841.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/test_reports/test_run_20250708_221909.log  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/minimal_test.cpython-312-pytest-8.4.1.pyc  
  inflating: /home/<USER>/dataset_generation/dataset_generation/__pycache__/simple_test.cpython-312-pytest-8.4.1.pyc
2025-07-09 03:02:32,486 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 03:02:32,487 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 03:02:32,487 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 03:02:32,572 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 03:02:32,572 - DEBUG - Secsh channel 3 opened.
2025-07-09 03:02:32,601 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 03:02:32,675 - DEBUG - [chan 3] EOF received (3)
2025-07-09 03:02:32,677 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 03:02:32,678 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 03:02:32,678 - DEBUG - STDOUT: 
2025-07-09 03:02:32,679 - DEBUG - Executing command: python3 --version
2025-07-09 03:02:32,680 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 03:02:32,764 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 03:02:32,764 - DEBUG - Secsh channel 4 opened.
2025-07-09 03:02:32,784 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 03:02:32,863 - DEBUG - [chan 4] EOF received (4)
2025-07-09 03:02:32,863 - INFO - Command executed successfully: python3 --version
2025-07-09 03:02:32,864 - DEBUG - STDOUT: Python 3.6.9
2025-07-09 03:02:32,864 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 03:02:32,864 - DEBUG - Executing command: sudo python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 03:02:32,865 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 03:02:32,949 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 03:02:32,949 - DEBUG - Secsh channel 5 opened.
2025-07-09 03:02:32,970 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 03:02:32,989 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 03:04:53,527 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 03:04:53,527 - DEBUG - Using password authentication
2025-07-09 03:04:53,594 - DEBUG - starting thread (client mode): 0x48c5ea80
2025-07-09 03:04:53,595 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 03:04:53,628 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 03:04:53,628 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 03:04:53,655 - DEBUG - === Key exchange possibilities ===
2025-07-09 03:04:53,655 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 03:04:53,656 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 03:04:53,656 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:04:53,657 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:04:53,657 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:04:53,657 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:04:53,658 - DEBUG - client compress: none, <EMAIL>
2025-07-09 03:04:53,658 - DEBUG - server compress: none, <EMAIL>
2025-07-09 03:04:53,659 - DEBUG - client lang: <none>
2025-07-09 03:04:53,659 - DEBUG - server lang: <none>
2025-07-09 03:04:53,660 - DEBUG - kex follows: False
2025-07-09 03:04:53,660 - DEBUG - === Key exchange agreements ===
2025-07-09 03:04:53,660 - DEBUG - Kex: <EMAIL>
2025-07-09 03:04:53,660 - DEBUG - HostKey: ssh-ed25519
2025-07-09 03:04:53,660 - DEBUG - Cipher: aes128-ctr
2025-07-09 03:04:53,660 - DEBUG - MAC: hmac-sha2-256
2025-07-09 03:04:53,660 - DEBUG - Compression: none
2025-07-09 03:04:53,660 - DEBUG - === End of kex handshake ===
2025-07-09 03:04:53,685 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 03:04:53,686 - DEBUG - Switch to new keys ...
2025-07-09 03:04:53,686 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 03:04:53,686 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 03:04:53,766 - DEBUG - userauth is OK
2025-07-09 03:04:53,791 - INFO - Authentication (password) successful!
2025-07-09 03:04:53,792 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 03:04:53,885 - DEBUG - Received global request "<EMAIL>"
2025-07-09 03:04:53,885 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 03:04:53,952 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 03:04:53,952 - DEBUG - Secsh channel 0 opened.
2025-07-09 03:04:53,971 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 03:04:53,993 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 03:04:53,993 - INFO - SSH connection established successfully
2025-07-09 03:04:53,993 - DEBUG - Executing command: echo $HOME
2025-07-09 03:04:53,993 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 03:04:54,018 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 03:04:54,019 - DEBUG - Secsh channel 1 opened.
2025-07-09 03:04:54,039 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 03:04:54,105 - DEBUG - [chan 1] EOF received (1)
2025-07-09 03:04:54,106 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 03:04:54,140 - INFO - Command executed successfully: echo $HOME
2025-07-09 03:04:54,263 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 03:04:54,286 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 03:04:54,569 - DEBUG - [chan 0] close(00000000)
2025-07-09 03:04:54,655 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 03:04:54,675 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:04:54,675 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 03:04:54,694 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 03:04:54,694 - DEBUG - Secsh channel 2 opened.
2025-07-09 03:04:54,733 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 03:04:54,779 - DEBUG - [chan 2] EOF received (2)
2025-07-09 03:04:54,779 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 03:04:54,834 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:04:54,834 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 03:04:54,835 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 03:04:54,866 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 03:04:54,866 - DEBUG - Secsh channel 3 opened.
2025-07-09 03:04:54,885 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 03:04:54,962 - DEBUG - [chan 3] EOF received (3)
2025-07-09 03:04:54,962 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 03:04:54,987 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 03:04:54,987 - DEBUG - Executing command: python3 --version
2025-07-09 03:04:54,987 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 03:04:55,060 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 03:04:55,061 - DEBUG - Secsh channel 4 opened.
2025-07-09 03:04:55,124 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 03:04:55,200 - DEBUG - [chan 4] EOF received (4)
2025-07-09 03:04:55,200 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 03:04:55,226 - INFO - Command executed successfully: python3 --version
2025-07-09 03:04:55,226 - DEBUG - Executing command: sudo python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 03:04:55,226 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 03:04:55,302 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 03:04:55,302 - DEBUG - Secsh channel 5 opened.
2025-07-09 03:04:55,331 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 03:04:55,351 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 03:07:45,309 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 03:07:45,309 - DEBUG - Using password authentication
2025-07-09 03:07:45,379 - DEBUG - starting thread (client mode): 0x150c3170
2025-07-09 03:07:45,379 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 03:07:45,404 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 03:07:45,404 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 03:07:45,426 - DEBUG - === Key exchange possibilities ===
2025-07-09 03:07:45,426 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 03:07:45,427 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 03:07:45,427 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:07:45,427 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:07:45,428 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:07:45,428 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:07:45,428 - DEBUG - client compress: none, <EMAIL>
2025-07-09 03:07:45,428 - DEBUG - server compress: none, <EMAIL>
2025-07-09 03:07:45,428 - DEBUG - client lang: <none>
2025-07-09 03:07:45,429 - DEBUG - server lang: <none>
2025-07-09 03:07:45,429 - DEBUG - kex follows: False
2025-07-09 03:07:45,429 - DEBUG - === Key exchange agreements ===
2025-07-09 03:07:45,430 - DEBUG - Kex: <EMAIL>
2025-07-09 03:07:45,430 - DEBUG - HostKey: ssh-ed25519
2025-07-09 03:07:45,430 - DEBUG - Cipher: aes128-ctr
2025-07-09 03:07:45,430 - DEBUG - MAC: hmac-sha2-256
2025-07-09 03:07:45,430 - DEBUG - Compression: none
2025-07-09 03:07:45,431 - DEBUG - === End of kex handshake ===
2025-07-09 03:07:45,456 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 03:07:45,456 - DEBUG - Switch to new keys ...
2025-07-09 03:07:45,457 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 03:07:45,457 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 03:07:45,540 - DEBUG - userauth is OK
2025-07-09 03:07:45,565 - INFO - Authentication (password) successful!
2025-07-09 03:07:45,566 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 03:07:45,659 - DEBUG - Received global request "<EMAIL>"
2025-07-09 03:07:45,660 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 03:07:45,729 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 03:07:45,729 - DEBUG - Secsh channel 0 opened.
2025-07-09 03:07:45,749 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 03:07:45,771 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 03:07:45,772 - INFO - SSH connection established successfully
2025-07-09 03:07:45,772 - DEBUG - Executing command: echo $HOME
2025-07-09 03:07:45,773 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 03:07:45,798 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 03:07:45,799 - DEBUG - Secsh channel 1 opened.
2025-07-09 03:07:45,822 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 03:07:45,892 - DEBUG - [chan 1] EOF received (1)
2025-07-09 03:07:45,892 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 03:07:45,923 - INFO - Command executed successfully: echo $HOME
2025-07-09 03:07:46,037 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 03:07:46,056 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 03:07:46,318 - DEBUG - [chan 0] close(00000000)
2025-07-09 03:07:46,380 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 03:07:46,400 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:07:46,400 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 03:07:46,419 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 03:07:46,419 - DEBUG - Secsh channel 2 opened.
2025-07-09 03:07:46,438 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 03:07:46,501 - DEBUG - [chan 2] EOF received (2)
2025-07-09 03:07:46,501 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 03:07:46,539 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:07:46,539 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 03:07:46,539 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 03:07:46,584 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 03:07:46,584 - DEBUG - Secsh channel 3 opened.
2025-07-09 03:07:46,603 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 03:07:46,683 - DEBUG - [chan 3] EOF received (3)
2025-07-09 03:07:46,683 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 03:07:46,705 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 03:07:46,705 - DEBUG - Executing command: python3 --version
2025-07-09 03:07:46,705 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 03:07:46,770 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 03:07:46,770 - DEBUG - Secsh channel 4 opened.
2025-07-09 03:07:46,804 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 03:07:46,864 - DEBUG - [chan 4] EOF received (4)
2025-07-09 03:07:46,865 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 03:07:46,905 - INFO - Command executed successfully: python3 --version
2025-07-09 03:07:46,905 - DEBUG - Executing command: sudo python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 03:07:46,905 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 03:07:46,947 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 03:07:46,948 - DEBUG - Secsh channel 5 opened.
2025-07-09 03:07:46,967 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 03:07:46,989 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 03:10:08,680 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 03:10:08,680 - DEBUG - Using password authentication
2025-07-09 03:10:08,764 - DEBUG - starting thread (client mode): 0xad518770
2025-07-09 03:10:08,764 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 03:10:08,788 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 03:10:08,789 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 03:10:08,814 - DEBUG - === Key exchange possibilities ===
2025-07-09 03:10:08,815 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 03:10:08,816 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 03:10:08,816 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:10:08,816 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:10:08,817 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:10:08,817 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:10:08,817 - DEBUG - client compress: none, <EMAIL>
2025-07-09 03:10:08,818 - DEBUG - server compress: none, <EMAIL>
2025-07-09 03:10:08,818 - DEBUG - client lang: <none>
2025-07-09 03:10:08,818 - DEBUG - server lang: <none>
2025-07-09 03:10:08,819 - DEBUG - kex follows: False
2025-07-09 03:10:08,819 - DEBUG - === Key exchange agreements ===
2025-07-09 03:10:08,819 - DEBUG - Kex: <EMAIL>
2025-07-09 03:10:08,819 - DEBUG - HostKey: ssh-ed25519
2025-07-09 03:10:08,820 - DEBUG - Cipher: aes128-ctr
2025-07-09 03:10:08,820 - DEBUG - MAC: hmac-sha2-256
2025-07-09 03:10:08,820 - DEBUG - Compression: none
2025-07-09 03:10:08,820 - DEBUG - === End of kex handshake ===
2025-07-09 03:10:08,846 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 03:10:08,846 - DEBUG - Switch to new keys ...
2025-07-09 03:10:08,847 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 03:10:08,847 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 03:10:08,935 - DEBUG - userauth is OK
2025-07-09 03:10:08,961 - INFO - Authentication (password) successful!
2025-07-09 03:10:08,962 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 03:10:09,056 - DEBUG - Received global request "<EMAIL>"
2025-07-09 03:10:09,057 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 03:10:09,131 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 03:10:09,131 - DEBUG - Secsh channel 0 opened.
2025-07-09 03:10:09,151 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 03:10:09,171 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 03:10:09,171 - INFO - SSH connection established successfully
2025-07-09 03:10:09,172 - DEBUG - Executing command: echo $HOME
2025-07-09 03:10:09,172 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 03:10:09,192 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 03:10:09,192 - DEBUG - Secsh channel 1 opened.
2025-07-09 03:10:09,214 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 03:10:09,281 - DEBUG - [chan 1] EOF received (1)
2025-07-09 03:10:09,282 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 03:10:09,315 - INFO - Command executed successfully: echo $HOME
2025-07-09 03:10:09,438 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 03:10:09,466 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 03:10:09,776 - DEBUG - [chan 0] close(00000000)
2025-07-09 03:10:09,838 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 03:10:09,858 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:10:09,859 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 03:10:09,880 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 03:10:09,880 - DEBUG - Secsh channel 2 opened.
2025-07-09 03:10:09,900 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 03:10:09,964 - DEBUG - [chan 2] EOF received (2)
2025-07-09 03:10:09,964 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 03:10:10,002 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:10:10,002 - DEBUG - Executing command: ls -R /home/<USER>/dataset_generation
2025-07-09 03:10:10,002 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 03:10:10,050 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 03:10:10,050 - DEBUG - Secsh channel 3 opened.
2025-07-09 03:10:10,070 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 03:10:10,092 - DEBUG - [chan 3] EOF received (3)
2025-07-09 03:10:10,093 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 03:10:10,171 - INFO - Command executed successfully: ls -R /home/<USER>/dataset_generation
2025-07-09 03:10:10,171 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 03:10:10,171 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 03:10:10,238 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 03:10:10,238 - DEBUG - Secsh channel 4 opened.
2025-07-09 03:10:10,266 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 03:10:10,328 - DEBUG - [chan 4] EOF received (4)
2025-07-09 03:10:10,328 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 03:10:10,367 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 03:10:10,367 - DEBUG - Executing command: python3 --version
2025-07-09 03:10:10,367 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 03:10:10,412 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 03:10:10,412 - DEBUG - Secsh channel 5 opened.
2025-07-09 03:10:10,432 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 03:10:10,502 - DEBUG - [chan 5] EOF received (5)
2025-07-09 03:10:10,503 - DEBUG - [chan 5] EOF sent (5)
2025-07-09 03:10:10,533 - INFO - Command executed successfully: python3 --version
2025-07-09 03:10:10,533 - DEBUG - Executing command: sudo python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 03:10:10,533 - DEBUG - [chan 6] Max packet in: 32768 bytes
2025-07-09 03:10:10,588 - DEBUG - [chan 6] Max packet out: 32768 bytes
2025-07-09 03:10:10,588 - DEBUG - Secsh channel 6 opened.
2025-07-09 03:10:10,610 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 03:10:10,630 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 03:10:55,568 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 03:10:55,568 - DEBUG - Using password authentication
2025-07-09 03:10:55,636 - DEBUG - starting thread (client mode): 0xd0d28680
2025-07-09 03:10:55,636 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 03:10:55,660 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 03:10:55,660 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 03:10:55,682 - DEBUG - === Key exchange possibilities ===
2025-07-09 03:10:55,683 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 03:10:55,683 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 03:10:55,684 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:10:55,684 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:10:55,684 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:10:55,685 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:10:55,685 - DEBUG - client compress: none, <EMAIL>
2025-07-09 03:10:55,686 - DEBUG - server compress: none, <EMAIL>
2025-07-09 03:10:55,686 - DEBUG - client lang: <none>
2025-07-09 03:10:55,687 - DEBUG - server lang: <none>
2025-07-09 03:10:55,687 - DEBUG - kex follows: False
2025-07-09 03:10:55,687 - DEBUG - === Key exchange agreements ===
2025-07-09 03:10:55,687 - DEBUG - Kex: <EMAIL>
2025-07-09 03:10:55,687 - DEBUG - HostKey: ssh-ed25519
2025-07-09 03:10:55,687 - DEBUG - Cipher: aes128-ctr
2025-07-09 03:10:55,687 - DEBUG - MAC: hmac-sha2-256
2025-07-09 03:10:55,687 - DEBUG - Compression: none
2025-07-09 03:10:55,687 - DEBUG - === End of kex handshake ===
2025-07-09 03:10:55,713 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 03:10:55,714 - DEBUG - Switch to new keys ...
2025-07-09 03:10:55,714 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 03:10:55,714 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 03:10:55,794 - DEBUG - userauth is OK
2025-07-09 03:10:55,816 - INFO - Authentication (password) successful!
2025-07-09 03:10:55,817 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 03:10:55,917 - DEBUG - Received global request "<EMAIL>"
2025-07-09 03:10:55,918 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 03:10:55,987 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 03:10:55,987 - DEBUG - Secsh channel 0 opened.
2025-07-09 03:10:56,008 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 03:10:56,029 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 03:10:56,029 - INFO - SSH connection established successfully
2025-07-09 03:10:56,029 - DEBUG - Executing command: echo $HOME
2025-07-09 03:10:56,029 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 03:10:56,048 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 03:10:56,048 - DEBUG - Secsh channel 1 opened.
2025-07-09 03:10:56,068 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 03:10:56,131 - DEBUG - [chan 1] EOF received (1)
2025-07-09 03:10:56,131 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 03:10:56,170 - INFO - Command executed successfully: echo $HOME
2025-07-09 03:10:56,285 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 03:10:56,310 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 03:10:56,642 - DEBUG - [chan 0] close(00000000)
2025-07-09 03:10:56,717 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 03:10:56,737 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:10:56,737 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 03:10:56,755 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 03:10:56,755 - DEBUG - Secsh channel 2 opened.
2025-07-09 03:10:56,776 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 03:10:56,819 - DEBUG - [chan 2] EOF received (2)
2025-07-09 03:10:56,819 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 03:10:56,877 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:10:56,877 - DEBUG - Executing command: ls -R /home/<USER>/dataset_generation
2025-07-09 03:10:56,877 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 03:10:56,902 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 03:10:56,904 - DEBUG - Secsh channel 3 opened.
2025-07-09 03:10:56,925 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 03:10:56,946 - DEBUG - [chan 3] EOF received (3)
2025-07-09 03:10:56,947 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 03:10:57,026 - INFO - Command executed successfully: ls -R /home/<USER>/dataset_generation
2025-07-09 03:10:57,026 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 03:10:57,026 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 03:10:57,093 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 03:10:57,093 - DEBUG - Secsh channel 4 opened.
2025-07-09 03:10:57,113 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 03:10:57,180 - DEBUG - [chan 4] EOF received (4)
2025-07-09 03:10:57,180 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 03:10:57,214 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 03:10:57,214 - DEBUG - Executing command: python3 --version
2025-07-09 03:10:57,214 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 03:10:57,269 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 03:10:57,269 - DEBUG - Secsh channel 5 opened.
2025-07-09 03:10:57,293 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 03:10:57,351 - DEBUG - [chan 5] EOF received (5)
2025-07-09 03:10:57,352 - DEBUG - [chan 5] EOF sent (5)
2025-07-09 03:10:57,394 - INFO - Command executed successfully: python3 --version
2025-07-09 03:10:57,394 - DEBUG - Executing command: sudo -S sudo python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 03:10:57,394 - DEBUG - [chan 6] Max packet in: 32768 bytes
2025-07-09 03:10:57,436 - DEBUG - [chan 6] Max packet out: 32768 bytes
2025-07-09 03:10:57,436 - DEBUG - Secsh channel 6 opened.
2025-07-09 03:10:57,455 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 03:10:57,475 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 03:11:17,526 - DEBUG - [chan 6] EOF received (6)
2025-07-09 03:11:17,526 - DEBUG - [chan 6] EOF sent (6)
2025-07-09 03:11:17,547 - ERROR - Command failed with exit code 1: sudo -S sudo python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 03:11:17,547 - INFO - [chan 0] sftp session closed.
2025-07-09 03:11:17,547 - DEBUG - [chan 0] EOF sent (0)
2025-07-09 03:11:17,547 - DEBUG - EOF in transport thread
2025-07-09 03:11:35,870 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 03:11:35,870 - DEBUG - Using password authentication
2025-07-09 03:11:38,617 - DEBUG - starting thread (client mode): 0xec2f8680
2025-07-09 03:11:38,618 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 03:11:38,621 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 03:11:38,622 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 03:11:38,687 - DEBUG - === Key exchange possibilities ===
2025-07-09 03:11:38,687 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 03:11:38,687 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 03:11:38,687 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:11:38,687 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:11:38,688 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:11:38,688 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:11:38,688 - DEBUG - client compress: none, <EMAIL>
2025-07-09 03:11:38,688 - DEBUG - server compress: none, <EMAIL>
2025-07-09 03:11:38,688 - DEBUG - client lang: <none>
2025-07-09 03:11:38,688 - DEBUG - server lang: <none>
2025-07-09 03:11:38,688 - DEBUG - kex follows: False
2025-07-09 03:11:38,688 - DEBUG - === Key exchange agreements ===
2025-07-09 03:11:38,688 - DEBUG - Kex: <EMAIL>
2025-07-09 03:11:38,688 - DEBUG - HostKey: ssh-ed25519
2025-07-09 03:11:38,688 - DEBUG - Cipher: aes128-ctr
2025-07-09 03:11:38,689 - DEBUG - MAC: hmac-sha2-256
2025-07-09 03:11:38,689 - DEBUG - Compression: none
2025-07-09 03:11:38,689 - DEBUG - === End of kex handshake ===
2025-07-09 03:11:38,776 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 03:11:38,777 - DEBUG - Switch to new keys ...
2025-07-09 03:11:38,777 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 03:11:38,777 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 03:11:38,858 - DEBUG - userauth is OK
2025-07-09 03:11:38,882 - INFO - Authentication (password) successful!
2025-07-09 03:11:38,883 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 03:11:38,975 - DEBUG - Received global request "<EMAIL>"
2025-07-09 03:11:38,975 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 03:11:39,048 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 03:11:39,048 - DEBUG - Secsh channel 0 opened.
2025-07-09 03:11:39,072 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 03:11:39,100 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 03:11:39,100 - INFO - SSH connection established successfully
2025-07-09 03:11:39,101 - DEBUG - Executing command: echo $HOME
2025-07-09 03:11:39,101 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 03:11:39,122 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 03:11:39,122 - DEBUG - Secsh channel 1 opened.
2025-07-09 03:11:39,160 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 03:11:39,234 - DEBUG - [chan 1] EOF received (1)
2025-07-09 03:11:39,235 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 03:11:39,261 - INFO - Command executed successfully: echo $HOME
2025-07-09 03:11:39,386 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 03:11:39,406 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 03:11:39,675 - DEBUG - [chan 0] close(00000000)
2025-07-09 03:11:39,745 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 03:11:39,765 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:11:39,765 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 03:11:39,784 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 03:11:39,784 - DEBUG - Secsh channel 2 opened.
2025-07-09 03:11:39,803 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 03:11:39,866 - DEBUG - [chan 2] EOF received (2)
2025-07-09 03:11:39,866 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 03:11:39,904 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:11:39,904 - DEBUG - Executing command: ls -R /home/<USER>/dataset_generation
2025-07-09 03:11:39,905 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 03:11:39,951 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 03:11:39,951 - DEBUG - Secsh channel 3 opened.
2025-07-09 03:11:39,974 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 03:11:39,998 - DEBUG - [chan 3] EOF received (3)
2025-07-09 03:11:39,999 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 03:11:40,075 - INFO - Command executed successfully: ls -R /home/<USER>/dataset_generation
2025-07-09 03:11:40,075 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 03:11:40,075 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 03:11:40,154 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 03:11:40,155 - DEBUG - Secsh channel 4 opened.
2025-07-09 03:11:40,174 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 03:11:40,244 - DEBUG - [chan 4] EOF received (4)
2025-07-09 03:11:40,245 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 03:11:40,275 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 03:11:40,275 - DEBUG - Executing command: python3 --version
2025-07-09 03:11:40,275 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 03:11:40,325 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 03:11:40,326 - DEBUG - Secsh channel 5 opened.
2025-07-09 03:11:40,345 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 03:11:40,418 - DEBUG - [chan 5] EOF received (5)
2025-07-09 03:11:40,418 - DEBUG - [chan 5] EOF sent (5)
2025-07-09 03:11:40,446 - INFO - Command executed successfully: python3 --version
2025-07-09 03:11:40,446 - DEBUG - Executing command: sudo -S sudo python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 03:11:40,446 - DEBUG - [chan 6] Max packet in: 32768 bytes
2025-07-09 03:11:40,500 - DEBUG - [chan 6] Max packet out: 32768 bytes
2025-07-09 03:11:40,500 - DEBUG - Secsh channel 6 opened.
2025-07-09 03:11:40,519 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 03:11:40,539 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 03:13:20,043 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 03:13:20,043 - DEBUG - Using password authentication
2025-07-09 03:13:20,113 - DEBUG - starting thread (client mode): 0xc6434200
2025-07-09 03:13:20,113 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 03:13:20,137 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 03:13:20,137 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 03:13:20,160 - DEBUG - === Key exchange possibilities ===
2025-07-09 03:13:20,161 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 03:13:20,161 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 03:13:20,161 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:13:20,162 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:13:20,162 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:13:20,163 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:13:20,163 - DEBUG - client compress: none, <EMAIL>
2025-07-09 03:13:20,163 - DEBUG - server compress: none, <EMAIL>
2025-07-09 03:13:20,164 - DEBUG - client lang: <none>
2025-07-09 03:13:20,164 - DEBUG - server lang: <none>
2025-07-09 03:13:20,164 - DEBUG - kex follows: False
2025-07-09 03:13:20,165 - DEBUG - === Key exchange agreements ===
2025-07-09 03:13:20,165 - DEBUG - Kex: <EMAIL>
2025-07-09 03:13:20,165 - DEBUG - HostKey: ssh-ed25519
2025-07-09 03:13:20,165 - DEBUG - Cipher: aes128-ctr
2025-07-09 03:13:20,165 - DEBUG - MAC: hmac-sha2-256
2025-07-09 03:13:20,165 - DEBUG - Compression: none
2025-07-09 03:13:20,165 - DEBUG - === End of kex handshake ===
2025-07-09 03:13:20,190 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 03:13:20,191 - DEBUG - Switch to new keys ...
2025-07-09 03:13:20,191 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 03:13:20,192 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 03:13:20,281 - DEBUG - userauth is OK
2025-07-09 03:13:20,301 - INFO - Authentication (password) successful!
2025-07-09 03:13:20,302 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 03:13:20,429 - DEBUG - Received global request "<EMAIL>"
2025-07-09 03:13:20,430 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 03:13:20,494 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 03:13:20,495 - DEBUG - Secsh channel 0 opened.
2025-07-09 03:13:20,514 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 03:13:20,534 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 03:13:20,534 - INFO - SSH connection established successfully
2025-07-09 03:13:20,534 - DEBUG - Executing command: echo $HOME
2025-07-09 03:13:20,534 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 03:13:20,554 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 03:13:20,555 - DEBUG - Secsh channel 1 opened.
2025-07-09 03:13:20,577 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 03:13:20,643 - DEBUG - [chan 1] EOF received (1)
2025-07-09 03:13:20,643 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 03:13:20,678 - INFO - Command executed successfully: echo $HOME
2025-07-09 03:13:20,795 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 03:13:20,815 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 03:13:21,087 - DEBUG - [chan 0] close(00000000)
2025-07-09 03:13:21,160 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 03:13:21,210 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:13:21,210 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 03:13:21,231 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 03:13:21,231 - DEBUG - Secsh channel 2 opened.
2025-07-09 03:13:21,252 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 03:13:21,326 - DEBUG - [chan 2] EOF received (2)
2025-07-09 03:13:21,326 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 03:13:21,354 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:13:21,354 - DEBUG - Executing command: ls -R /home/<USER>/dataset_generation
2025-07-09 03:13:21,354 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 03:13:21,407 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 03:13:21,408 - DEBUG - Secsh channel 3 opened.
2025-07-09 03:13:21,428 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 03:13:21,451 - DEBUG - [chan 3] EOF received (3)
2025-07-09 03:13:21,451 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 03:13:21,529 - INFO - Command executed successfully: ls -R /home/<USER>/dataset_generation
2025-07-09 03:13:21,529 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 03:13:21,529 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 03:13:21,604 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 03:13:21,604 - DEBUG - Secsh channel 4 opened.
2025-07-09 03:13:21,624 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 03:13:21,695 - DEBUG - [chan 4] EOF received (4)
2025-07-09 03:13:21,696 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 03:13:21,725 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 03:13:21,725 - DEBUG - Executing command: python3 --version
2025-07-09 03:13:21,725 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 03:13:21,778 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 03:13:21,778 - DEBUG - Secsh channel 5 opened.
2025-07-09 03:13:21,803 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 03:13:21,868 - DEBUG - [chan 5] EOF received (5)
2025-07-09 03:13:21,868 - DEBUG - [chan 5] EOF sent (5)
2025-07-09 03:13:21,904 - INFO - Command executed successfully: python3 --version
2025-07-09 03:13:21,904 - DEBUG - Executing command: sudo -S sudo python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 03:13:21,905 - DEBUG - [chan 6] Max packet in: 32768 bytes
2025-07-09 03:13:21,949 - DEBUG - [chan 6] Max packet out: 32768 bytes
2025-07-09 03:13:21,949 - DEBUG - Secsh channel 6 opened.
2025-07-09 03:13:21,970 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 03:13:21,990 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 03:13:42,359 - DEBUG - [chan 6] EOF received (6)
2025-07-09 03:13:42,360 - DEBUG - [chan 6] EOF sent (6)
2025-07-09 03:13:42,372 - ERROR - Command failed with exit code 1: sudo -S sudo python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 03:13:42,373 - INFO - [chan 0] sftp session closed.
2025-07-09 03:13:42,373 - DEBUG - [chan 0] EOF sent (0)
2025-07-09 03:13:42,375 - DEBUG - EOF in transport thread
2025-07-09 03:14:02,575 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 03:14:02,575 - DEBUG - Using password authentication
2025-07-09 03:14:02,641 - DEBUG - starting thread (client mode): 0xc8a04200
2025-07-09 03:14:02,642 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 03:14:02,646 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 03:14:02,646 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 03:14:02,715 - DEBUG - === Key exchange possibilities ===
2025-07-09 03:14:02,716 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 03:14:02,716 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 03:14:02,716 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:14:02,716 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:14:02,716 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:14:02,716 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:14:02,717 - DEBUG - client compress: none, <EMAIL>
2025-07-09 03:14:02,717 - DEBUG - server compress: none, <EMAIL>
2025-07-09 03:14:02,717 - DEBUG - client lang: <none>
2025-07-09 03:14:02,717 - DEBUG - server lang: <none>
2025-07-09 03:14:02,717 - DEBUG - kex follows: False
2025-07-09 03:14:02,717 - DEBUG - === Key exchange agreements ===
2025-07-09 03:14:02,718 - DEBUG - Kex: <EMAIL>
2025-07-09 03:14:02,718 - DEBUG - HostKey: ssh-ed25519
2025-07-09 03:14:02,718 - DEBUG - Cipher: aes128-ctr
2025-07-09 03:14:02,718 - DEBUG - MAC: hmac-sha2-256
2025-07-09 03:14:02,718 - DEBUG - Compression: none
2025-07-09 03:14:02,719 - DEBUG - === End of kex handshake ===
2025-07-09 03:14:02,805 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 03:14:02,807 - DEBUG - Switch to new keys ...
2025-07-09 03:14:02,808 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 03:14:02,808 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 03:14:02,885 - DEBUG - userauth is OK
2025-07-09 03:14:02,907 - INFO - Authentication (password) successful!
2025-07-09 03:14:02,907 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 03:14:03,003 - DEBUG - Received global request "<EMAIL>"
2025-07-09 03:14:03,004 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 03:14:03,071 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 03:14:03,072 - DEBUG - Secsh channel 0 opened.
2025-07-09 03:14:03,092 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 03:14:03,112 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 03:14:03,112 - INFO - SSH connection established successfully
2025-07-09 03:14:03,112 - DEBUG - Executing command: echo $HOME
2025-07-09 03:14:03,112 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 03:14:03,132 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 03:14:03,133 - DEBUG - Secsh channel 1 opened.
2025-07-09 03:14:03,153 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 03:14:03,227 - DEBUG - [chan 1] EOF received (1)
2025-07-09 03:14:03,227 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 03:14:03,255 - INFO - Command executed successfully: echo $HOME
2025-07-09 03:14:03,373 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 03:14:03,393 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 03:14:03,666 - DEBUG - [chan 0] close(00000000)
2025-07-09 03:14:03,738 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 03:14:03,758 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:14:03,758 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 03:14:03,778 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 03:14:03,778 - DEBUG - Secsh channel 2 opened.
2025-07-09 03:14:03,799 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 03:14:03,866 - DEBUG - [chan 2] EOF received (2)
2025-07-09 03:14:03,866 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 03:14:03,901 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:14:03,901 - DEBUG - Executing command: ls -R /home/<USER>/dataset_generation
2025-07-09 03:14:03,901 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 03:14:03,954 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 03:14:03,954 - DEBUG - Secsh channel 3 opened.
2025-07-09 03:14:03,979 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 03:14:04,066 - DEBUG - [chan 3] EOF received (3)
2025-07-09 03:14:04,066 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 03:14:04,080 - INFO - Command executed successfully: ls -R /home/<USER>/dataset_generation
2025-07-09 03:14:04,080 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 03:14:04,080 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 03:14:04,149 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 03:14:04,150 - DEBUG - Secsh channel 4 opened.
2025-07-09 03:14:04,174 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 03:14:04,239 - DEBUG - [chan 4] EOF received (4)
2025-07-09 03:14:04,240 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 03:14:04,275 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 03:14:04,275 - DEBUG - Executing command: python3 --version
2025-07-09 03:14:04,275 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 03:14:04,321 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 03:14:04,322 - DEBUG - Secsh channel 5 opened.
2025-07-09 03:14:04,341 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 03:14:04,410 - DEBUG - [chan 5] EOF received (5)
2025-07-09 03:14:04,410 - DEBUG - [chan 5] EOF sent (5)
2025-07-09 03:14:04,442 - INFO - Command executed successfully: python3 --version
2025-07-09 03:14:04,442 - DEBUG - Executing command: sudo -S sudo python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 03:14:04,442 - DEBUG - [chan 6] Max packet in: 32768 bytes
2025-07-09 03:14:04,492 - DEBUG - [chan 6] Max packet out: 32768 bytes
2025-07-09 03:14:04,492 - DEBUG - Secsh channel 6 opened.
2025-07-09 03:14:04,512 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 03:14:04,538 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 03:14:24,661 - DEBUG - [chan 6] EOF received (6)
2025-07-09 03:14:24,661 - DEBUG - [chan 6] EOF sent (6)
2025-07-09 03:14:24,722 - ERROR - Command failed with exit code 1: sudo -S sudo python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 03:14:24,722 - INFO - [chan 0] sftp session closed.
2025-07-09 03:14:24,722 - DEBUG - [chan 0] EOF sent (0)
2025-07-09 03:14:24,722 - DEBUG - EOF in transport thread
2025-07-09 03:15:22,904 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 03:15:22,904 - DEBUG - Using password authentication
2025-07-09 03:15:22,987 - DEBUG - starting thread (client mode): 0xda1b8680
2025-07-09 03:15:22,987 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 03:15:22,988 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 03:15:22,988 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 03:15:23,051 - DEBUG - === Key exchange possibilities ===
2025-07-09 03:15:23,052 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 03:15:23,052 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 03:15:23,053 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:15:23,053 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:15:23,054 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:15:23,054 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:15:23,054 - DEBUG - client compress: none, <EMAIL>
2025-07-09 03:15:23,055 - DEBUG - server compress: none, <EMAIL>
2025-07-09 03:15:23,055 - DEBUG - client lang: <none>
2025-07-09 03:15:23,055 - DEBUG - server lang: <none>
2025-07-09 03:15:23,056 - DEBUG - kex follows: False
2025-07-09 03:15:23,056 - DEBUG - === Key exchange agreements ===
2025-07-09 03:15:23,057 - DEBUG - Kex: <EMAIL>
2025-07-09 03:15:23,057 - DEBUG - HostKey: ssh-ed25519
2025-07-09 03:15:23,057 - DEBUG - Cipher: aes128-ctr
2025-07-09 03:15:23,058 - DEBUG - MAC: hmac-sha2-256
2025-07-09 03:15:23,058 - DEBUG - Compression: none
2025-07-09 03:15:23,059 - DEBUG - === End of kex handshake ===
2025-07-09 03:15:23,140 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 03:15:23,142 - DEBUG - Switch to new keys ...
2025-07-09 03:15:23,144 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 03:15:23,145 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 03:15:23,221 - DEBUG - userauth is OK
2025-07-09 03:15:23,242 - INFO - Authentication (password) successful!
2025-07-09 03:15:23,242 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 03:15:23,341 - DEBUG - Received global request "<EMAIL>"
2025-07-09 03:15:23,341 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 03:15:23,414 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 03:15:23,414 - DEBUG - Secsh channel 0 opened.
2025-07-09 03:15:23,437 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 03:15:23,460 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 03:15:23,461 - INFO - SSH connection established successfully
2025-07-09 03:15:23,461 - DEBUG - Executing command: echo $HOME
2025-07-09 03:15:23,462 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 03:15:23,489 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 03:15:23,490 - DEBUG - Secsh channel 1 opened.
2025-07-09 03:15:23,515 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 03:15:23,632 - DEBUG - [chan 1] EOF received (1)
2025-07-09 03:15:23,632 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 03:15:23,717 - INFO - Command executed successfully: echo $HOME
2025-07-09 03:15:23,837 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 03:15:23,861 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 03:15:24,143 - DEBUG - [chan 0] close(00000000)
2025-07-09 03:15:24,212 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 03:15:24,232 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:15:24,233 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 03:15:24,255 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 03:15:24,256 - DEBUG - Secsh channel 2 opened.
2025-07-09 03:15:24,277 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 03:15:24,351 - DEBUG - [chan 2] EOF received (2)
2025-07-09 03:15:24,352 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 03:15:24,378 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:15:24,378 - DEBUG - Executing command: ls -R /home/<USER>/dataset_generation
2025-07-09 03:15:24,379 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 03:15:24,439 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 03:15:24,439 - DEBUG - Secsh channel 3 opened.
2025-07-09 03:15:24,459 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 03:15:24,522 - DEBUG - [chan 3] EOF received (3)
2025-07-09 03:15:24,522 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 03:15:24,561 - INFO - Command executed successfully: ls -R /home/<USER>/dataset_generation
2025-07-09 03:15:24,561 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 03:15:24,561 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 03:15:24,606 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 03:15:24,606 - DEBUG - Secsh channel 4 opened.
2025-07-09 03:15:24,625 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 03:15:24,694 - DEBUG - [chan 4] EOF received (4)
2025-07-09 03:15:24,695 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 03:15:24,726 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 03:15:24,726 - DEBUG - Executing command: sudo -S sudo mn -c
2025-07-09 03:15:24,727 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 03:15:24,786 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 03:15:24,786 - DEBUG - Secsh channel 5 opened.
2025-07-09 03:15:24,805 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 03:15:30,000 - DEBUG - [chan 5] EOF received (5)
2025-07-09 03:15:30,001 - DEBUG - [chan 5] EOF sent (5)
2025-07-09 03:15:30,026 - INFO - Command executed successfully: sudo -S sudo mn -c
2025-07-09 03:15:30,026 - DEBUG - Executing command: python3 --version
2025-07-09 03:15:30,027 - DEBUG - [chan 6] Max packet in: 32768 bytes
2025-07-09 03:15:30,087 - DEBUG - [chan 6] Max packet out: 32768 bytes
2025-07-09 03:15:30,087 - DEBUG - Secsh channel 6 opened.
2025-07-09 03:15:30,107 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 03:15:30,173 - DEBUG - [chan 6] EOF received (6)
2025-07-09 03:15:30,173 - DEBUG - [chan 6] EOF sent (6)
2025-07-09 03:15:30,208 - INFO - Command executed successfully: python3 --version
2025-07-09 03:15:30,208 - DEBUG - Executing command: sudo -S sudo python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 03:15:30,208 - DEBUG - [chan 7] Max packet in: 32768 bytes
2025-07-09 03:15:30,256 - DEBUG - [chan 7] Max packet out: 32768 bytes
2025-07-09 03:15:30,256 - DEBUG - Secsh channel 7 opened.
2025-07-09 03:15:30,276 - DEBUG - [chan 7] Sesch channel 7 request ok
2025-07-09 03:15:30,297 - DEBUG - [chan 7] Sesch channel 7 request ok
2025-07-09 03:16:35,298 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 03:16:35,299 - DEBUG - Using password authentication
2025-07-09 03:16:35,362 - DEBUG - starting thread (client mode): 0x849b8680
2025-07-09 03:16:35,362 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 03:16:35,385 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 03:16:35,386 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 03:16:35,407 - DEBUG - === Key exchange possibilities ===
2025-07-09 03:16:35,408 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 03:16:35,408 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 03:16:35,409 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:16:35,409 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:16:35,409 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:16:35,410 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:16:35,410 - DEBUG - client compress: none, <EMAIL>
2025-07-09 03:16:35,410 - DEBUG - server compress: none, <EMAIL>
2025-07-09 03:16:35,412 - DEBUG - client lang: <none>
2025-07-09 03:16:35,412 - DEBUG - server lang: <none>
2025-07-09 03:16:35,413 - DEBUG - kex follows: False
2025-07-09 03:16:35,413 - DEBUG - === Key exchange agreements ===
2025-07-09 03:16:35,413 - DEBUG - Kex: <EMAIL>
2025-07-09 03:16:35,413 - DEBUG - HostKey: ssh-ed25519
2025-07-09 03:16:35,413 - DEBUG - Cipher: aes128-ctr
2025-07-09 03:16:35,413 - DEBUG - MAC: hmac-sha2-256
2025-07-09 03:16:35,414 - DEBUG - Compression: none
2025-07-09 03:16:35,414 - DEBUG - === End of kex handshake ===
2025-07-09 03:16:35,439 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 03:16:35,440 - DEBUG - Switch to new keys ...
2025-07-09 03:16:35,440 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 03:16:35,441 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 03:16:35,524 - DEBUG - userauth is OK
2025-07-09 03:16:35,545 - INFO - Authentication (password) successful!
2025-07-09 03:16:35,545 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 03:16:35,665 - DEBUG - Received global request "<EMAIL>"
2025-07-09 03:16:35,666 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 03:16:35,733 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 03:16:35,734 - DEBUG - Secsh channel 0 opened.
2025-07-09 03:16:35,755 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 03:16:35,783 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 03:16:35,783 - INFO - SSH connection established successfully
2025-07-09 03:16:35,783 - DEBUG - Executing command: echo $HOME
2025-07-09 03:16:35,784 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 03:16:35,803 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 03:16:35,803 - DEBUG - Secsh channel 1 opened.
2025-07-09 03:16:35,823 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 03:16:35,887 - DEBUG - [chan 1] EOF received (1)
2025-07-09 03:16:35,888 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 03:16:35,923 - INFO - Command executed successfully: echo $HOME
2025-07-09 03:16:36,043 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 03:16:36,067 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 03:16:36,343 - DEBUG - [chan 0] close(00000000)
2025-07-09 03:16:36,421 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 03:16:36,447 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:16:36,448 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 03:16:36,469 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 03:16:36,469 - DEBUG - Secsh channel 2 opened.
2025-07-09 03:16:36,491 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 03:16:36,542 - DEBUG - [chan 2] EOF received (2)
2025-07-09 03:16:36,543 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 03:16:36,592 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:16:36,592 - DEBUG - Executing command: ls -R /home/<USER>/dataset_generation
2025-07-09 03:16:36,592 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 03:16:36,628 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 03:16:36,628 - DEBUG - Secsh channel 3 opened.
2025-07-09 03:16:36,649 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 03:16:36,670 - DEBUG - [chan 3] EOF received (3)
2025-07-09 03:16:36,670 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 03:16:36,750 - INFO - Command executed successfully: ls -R /home/<USER>/dataset_generation
2025-07-09 03:16:36,750 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 03:16:36,750 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 03:16:36,813 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 03:16:36,813 - DEBUG - Secsh channel 4 opened.
2025-07-09 03:16:36,835 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 03:16:36,909 - DEBUG - [chan 4] EOF received (4)
2025-07-09 03:16:36,909 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 03:16:36,936 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 03:16:36,936 - DEBUG - Executing command: sudo -S sudo mn -c
2025-07-09 03:16:36,936 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 03:16:36,988 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 03:16:36,988 - DEBUG - Secsh channel 5 opened.
2025-07-09 03:16:37,012 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 03:16:42,470 - DEBUG - [chan 5] EOF received (5)
2025-07-09 03:16:42,470 - DEBUG - [chan 5] EOF sent (5)
2025-07-09 03:16:42,540 - INFO - Command executed successfully: sudo -S sudo mn -c
2025-07-09 03:16:42,540 - DEBUG - Executing command: python3 --version
2025-07-09 03:16:42,540 - DEBUG - [chan 6] Max packet in: 32768 bytes
2025-07-09 03:16:42,608 - DEBUG - [chan 6] Max packet out: 32768 bytes
2025-07-09 03:16:42,609 - DEBUG - Secsh channel 6 opened.
2025-07-09 03:16:42,628 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 03:16:42,691 - DEBUG - [chan 6] EOF received (6)
2025-07-09 03:16:42,691 - DEBUG - [chan 6] EOF sent (6)
2025-07-09 03:16:42,729 - INFO - Command executed successfully: python3 --version
2025-07-09 03:16:42,729 - DEBUG - Executing command: sudo -S sudo python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 03:16:42,729 - DEBUG - [chan 7] Max packet in: 32768 bytes
2025-07-09 03:16:42,776 - DEBUG - [chan 7] Max packet out: 32768 bytes
2025-07-09 03:16:42,776 - DEBUG - Secsh channel 7 opened.
2025-07-09 03:16:42,796 - DEBUG - [chan 7] Sesch channel 7 request ok
2025-07-09 03:16:42,816 - DEBUG - [chan 7] Sesch channel 7 request ok
2025-07-09 03:17:24,216 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 03:17:24,217 - DEBUG - Using password authentication
2025-07-09 03:17:24,709 - DEBUG - starting thread (client mode): 0x1b218680
2025-07-09 03:17:24,709 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 03:17:24,735 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 03:17:24,735 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 03:17:24,758 - DEBUG - === Key exchange possibilities ===
2025-07-09 03:17:24,758 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 03:17:24,758 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 03:17:24,758 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:17:24,758 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 03:17:24,758 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:17:24,758 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 03:17:24,758 - DEBUG - client compress: none, <EMAIL>
2025-07-09 03:17:24,759 - DEBUG - server compress: none, <EMAIL>
2025-07-09 03:17:24,759 - DEBUG - client lang: <none>
2025-07-09 03:17:24,759 - DEBUG - server lang: <none>
2025-07-09 03:17:24,759 - DEBUG - kex follows: False
2025-07-09 03:17:24,759 - DEBUG - === Key exchange agreements ===
2025-07-09 03:17:24,759 - DEBUG - Kex: <EMAIL>
2025-07-09 03:17:24,759 - DEBUG - HostKey: ssh-ed25519
2025-07-09 03:17:24,759 - DEBUG - Cipher: aes128-ctr
2025-07-09 03:17:24,759 - DEBUG - MAC: hmac-sha2-256
2025-07-09 03:17:24,760 - DEBUG - Compression: none
2025-07-09 03:17:24,760 - DEBUG - === End of kex handshake ===
2025-07-09 03:17:24,786 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 03:17:24,787 - DEBUG - Switch to new keys ...
2025-07-09 03:17:24,788 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 03:17:24,788 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 03:17:24,866 - DEBUG - userauth is OK
2025-07-09 03:17:24,887 - INFO - Authentication (password) successful!
2025-07-09 03:17:24,888 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 03:17:25,015 - DEBUG - Received global request "<EMAIL>"
2025-07-09 03:17:25,015 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 03:17:25,075 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 03:17:25,076 - DEBUG - Secsh channel 0 opened.
2025-07-09 03:17:25,097 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 03:17:25,125 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-09 03:17:25,125 - INFO - SSH connection established successfully
2025-07-09 03:17:25,126 - DEBUG - Executing command: echo $HOME
2025-07-09 03:17:25,126 - DEBUG - [chan 1] Max packet in: 32768 bytes
2025-07-09 03:17:25,147 - DEBUG - [chan 1] Max packet out: 32768 bytes
2025-07-09 03:17:25,147 - DEBUG - Secsh channel 1 opened.
2025-07-09 03:17:25,168 - DEBUG - [chan 1] Sesch channel 1 request ok
2025-07-09 03:17:25,232 - DEBUG - [chan 1] EOF received (1)
2025-07-09 03:17:25,233 - DEBUG - [chan 1] EOF sent (1)
2025-07-09 03:17:25,270 - INFO - Command executed successfully: echo $HOME
2025-07-09 03:17:25,397 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb')
2025-07-09 03:17:25,420 - DEBUG - [chan 0] open(b'/home/<USER>/project.zip', 'wb') -> 00000000
2025-07-09 03:17:25,694 - DEBUG - [chan 0] close(00000000)
2025-07-09 03:17:25,757 - DEBUG - [chan 0] stat(b'/home/<USER>/project.zip')
2025-07-09 03:17:25,778 - DEBUG - Executing command: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:17:25,779 - DEBUG - [chan 2] Max packet in: 32768 bytes
2025-07-09 03:17:25,802 - DEBUG - [chan 2] Max packet out: 32768 bytes
2025-07-09 03:17:25,802 - DEBUG - Secsh channel 2 opened.
2025-07-09 03:17:25,823 - DEBUG - [chan 2] Sesch channel 2 request ok
2025-07-09 03:17:25,870 - DEBUG - [chan 2] EOF received (2)
2025-07-09 03:17:25,870 - DEBUG - [chan 2] EOF sent (2)
2025-07-09 03:17:25,925 - INFO - Command executed successfully: unzip -o /home/<USER>/project.zip -d /home/<USER>/dataset_generation
2025-07-09 03:17:25,925 - DEBUG - Executing command: ls -R /home/<USER>/dataset_generation
2025-07-09 03:17:25,926 - DEBUG - [chan 3] Max packet in: 32768 bytes
2025-07-09 03:17:25,960 - DEBUG - [chan 3] Max packet out: 32768 bytes
2025-07-09 03:17:25,960 - DEBUG - Secsh channel 3 opened.
2025-07-09 03:17:26,013 - DEBUG - [chan 3] Sesch channel 3 request ok
2025-07-09 03:17:26,032 - DEBUG - [chan 3] EOF received (3)
2025-07-09 03:17:26,033 - DEBUG - [chan 3] EOF sent (3)
2025-07-09 03:17:26,115 - INFO - Command executed successfully: ls -R /home/<USER>/dataset_generation
2025-07-09 03:17:26,115 - DEBUG - Executing command: mkdir -p ~/dataset_generation
2025-07-09 03:17:26,115 - DEBUG - [chan 4] Max packet in: 32768 bytes
2025-07-09 03:17:26,183 - DEBUG - [chan 4] Max packet out: 32768 bytes
2025-07-09 03:17:26,183 - DEBUG - Secsh channel 4 opened.
2025-07-09 03:17:26,203 - DEBUG - [chan 4] Sesch channel 4 request ok
2025-07-09 03:17:26,268 - DEBUG - [chan 4] EOF received (4)
2025-07-09 03:17:26,268 - DEBUG - [chan 4] EOF sent (4)
2025-07-09 03:17:26,304 - INFO - Command executed successfully: mkdir -p ~/dataset_generation
2025-07-09 03:17:26,304 - DEBUG - Executing command: sudo -S sudo mn -c
2025-07-09 03:17:26,304 - DEBUG - [chan 5] Max packet in: 32768 bytes
2025-07-09 03:17:26,348 - DEBUG - [chan 5] Max packet out: 32768 bytes
2025-07-09 03:17:26,350 - DEBUG - Secsh channel 5 opened.
2025-07-09 03:17:26,370 - DEBUG - [chan 5] Sesch channel 5 request ok
2025-07-09 03:17:30,386 - ERROR - Error executing command: [Errno 22] Invalid argument
2025-07-09 03:17:30,387 - DEBUG - [chan 5] EOF sent (5)
2025-07-09 03:17:30,388 - DEBUG - Executing command: python3 --version
2025-07-09 03:17:30,388 - DEBUG - [chan 6] Max packet in: 32768 bytes
2025-07-09 03:17:30,470 - DEBUG - [chan 6] Max packet out: 32768 bytes
2025-07-09 03:17:30,471 - DEBUG - Secsh channel 6 opened.
2025-07-09 03:17:30,492 - DEBUG - [chan 6] Sesch channel 6 request ok
2025-07-09 03:17:30,512 - DEBUG - [chan 6] EOF received (6)
2025-07-09 03:17:30,512 - DEBUG - [chan 6] EOF sent (6)
2025-07-09 03:17:30,594 - INFO - Command executed successfully: python3 --version
2025-07-09 03:17:30,594 - DEBUG - Executing command: sudo -S sudo python3 /home/<USER>/dataset_generation/dataset_generation/main.py
2025-07-09 03:17:30,595 - DEBUG - [chan 7] Max packet in: 32768 bytes
2025-07-09 03:17:30,662 - DEBUG - [chan 7] Max packet out: 32768 bytes
2025-07-09 03:17:30,662 - DEBUG - Secsh channel 7 opened.
2025-07-09 03:17:30,689 - DEBUG - [chan 7] Sesch channel 7 request ok
2025-07-09 03:17:30,710 - DEBUG - [chan 7] Sesch channel 7 request ok
2025-07-09 03:17:30,812 - ERROR - Error executing command: [Errno 22] Invalid argument
2025-07-09 03:17:30,812 - DEBUG - [chan 7] EOF sent (7)
2025-07-09 03:17:30,813 - INFO - [chan 0] sftp session closed.
2025-07-09 03:17:30,814 - DEBUG - [chan 0] EOF sent (0)
2025-07-09 03:17:30,815 - DEBUG - EOF in transport thread
