#!/usr/bin/env python3
import sys
import os
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_debug.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

try:
    # Add parent directory to path to import test module
    sys.path.append(str(Path(__file__).parent))
    from src.tests.integration.test_remote_exec import TestRemoteCLI
    
    # Run the test
    logger.info("Starting test...")
    test = TestRemoteCLI('test_connection')
    test.setUpClass()
    test.test_connection()
    logger.info("Test completed successfully")
    
except Exception as e:
    logger.error(f"Test failed: {str(e)}", exc_info=True)
    sys.exit(1)
