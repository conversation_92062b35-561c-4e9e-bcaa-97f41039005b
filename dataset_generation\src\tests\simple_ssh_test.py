#!/usr/bin/env python3
"""
Simple SSH connection test script.
"""
import sys
import paramiko

def main():
    # Open a file to write the output
    with open('ssh_test_output.txt', 'w') as f:
        f.write("Starting SSH connection test...\n")
        
        hostname = 'jtmksrv'  # Replace with your server hostname or IP
        port = 656            # Default SSH port is 22
        username = 'user'     # Replace with your username
        password = '1'        # Replace with your password
        
        f.write(f"Connecting to {username}@{hostname}:{port}...\n")
        
        # Create SSH client
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        try:
            f.write("Attempting to connect...\n")
            ssh.connect(
                hostname=hostname,
                port=port,
                username=username,
                password=password,
                timeout=10,
                banner_timeout=30,
                auth_timeout=10,
                allow_agent=False,
                look_for_keys=False
            )
            f.write("SSH connection successful!\n")
            
            # Test command execution
            f.write("\nTesting command execution...\n")
            stdin, stdout, stderr = ssh.exec_command("echo 'Hello, World!'")
            exit_code = stdout.channel.recv_exit_status()
            
            f.write(f"Exit code: {exit_code}\n")
            stdout_output = stdout.read().decode().strip()
            f.write(f"STDOUT: {stdout_output}\n")
            
            error_output = stderr.read().decode().strip()
            if error_output:
                f.write(f"STDERR: {error_output}\n")
            
        except Exception as e:
            f.write(f"Error: {str(e)}\n")
            import traceback
            f.write("Traceback:\n")
            f.write(traceback.format_exc())
        finally:
            ssh.close()
            f.write("\nConnection closed.\n")
            f.write("Test completed.\n")

if __name__ == "__main__":
    main()
    print("Test completed. Check 'ssh_test_output.txt' for details.")
