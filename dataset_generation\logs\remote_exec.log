2025-07-09 02:01:29,678 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 02:01:29,678 - DEBUG - Using password authentication
2025-07-09 02:01:29,678 - DEBUG - Connecting with parameters: {'hostname': 'jtmksrv', 'port': 656, 'username': 'user', 'timeout': 10, 'banner_timeout': 60, 'auth_timeout': 30, 'allow_agent': Fals<PERSON>, 'look_for_keys': False, 'password': '***'}
2025-07-09 02:01:30,167 - DEBUG - starting thread (client mode): 0xc21c9c10
2025-07-09 02:01:30,167 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 02:01:30,190 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 02:01:30,193 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 02:01:30,218 - DEBUG - === Key exchange possibilities ===
2025-07-09 02:01:30,218 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 02:01:30,218 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 02:01:30,218 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:01:30,218 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:01:30,218 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:01:30,219 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:01:30,219 - DEBUG - client compress: none, <EMAIL>
2025-07-09 02:01:30,219 - DEBUG - server compress: none, <EMAIL>
2025-07-09 02:01:30,219 - DEBUG - client lang: <none>
2025-07-09 02:01:30,219 - DEBUG - server lang: <none>
2025-07-09 02:01:30,219 - DEBUG - kex follows: False
2025-07-09 02:01:30,219 - DEBUG - === Key exchange agreements ===
2025-07-09 02:01:30,220 - DEBUG - Kex: <EMAIL>
2025-07-09 02:01:30,220 - DEBUG - HostKey: ssh-ed25519
2025-07-09 02:01:30,220 - DEBUG - Cipher: aes128-ctr
2025-07-09 02:01:30,220 - DEBUG - MAC: hmac-sha2-256
2025-07-09 02:01:30,220 - DEBUG - Compression: none
2025-07-09 02:01:30,220 - DEBUG - === End of kex handshake ===
2025-07-09 02:01:30,245 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 02:01:30,246 - DEBUG - Switch to new keys ...
2025-07-09 02:01:30,246 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 02:01:30,246 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 02:01:30,325 - DEBUG - userauth is OK
2025-07-09 02:01:30,346 - INFO - Authentication (password) successful!
2025-07-09 02:01:30,348 - INFO - SSH connection established successfully
2025-07-09 02:01:30,348 - DEBUG - Executing command: echo 'Hello, World!'
2025-07-09 02:01:30,348 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 02:01:30,439 - DEBUG - Received global request "<EMAIL>"
2025-07-09 02:01:30,439 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 02:01:30,500 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 02:01:30,500 - DEBUG - Secsh channel 0 opened.
2025-07-09 02:01:30,579 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 02:01:30,642 - DEBUG - [chan 0] EOF received (0)
2025-07-09 02:01:30,643 - DEBUG - Command completed successfully (status: 0)
2025-07-09 02:01:30,643 - DEBUG - [chan 0] EOF sent (0)
2025-07-09 02:01:30,643 - DEBUG - Command output: Hello, World!
2025-07-09 02:01:30,643 - DEBUG - EOF in transport thread
2025-07-09 02:28:04,484 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 02:28:04,485 - DEBUG - Using password authentication
2025-07-09 02:28:04,485 - DEBUG - Connecting with parameters: {'hostname': 'jtmksrv', 'port': 656, 'username': 'user', 'timeout': 10, 'banner_timeout': 60, 'auth_timeout': 30, 'allow_agent': False, 'look_for_keys': False, 'password': '***'}
2025-07-09 02:28:04,555 - DEBUG - starting thread (client mode): 0xd10c8680
2025-07-09 02:28:04,555 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 02:28:04,564 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 02:28:04,564 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 02:28:04,634 - DEBUG - === Key exchange possibilities ===
2025-07-09 02:28:04,634 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 02:28:04,634 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 02:28:04,634 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:28:04,634 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:28:04,634 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:28:04,635 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:28:04,635 - DEBUG - client compress: none, <EMAIL>
2025-07-09 02:28:04,635 - DEBUG - server compress: none, <EMAIL>
2025-07-09 02:28:04,636 - DEBUG - client lang: <none>
2025-07-09 02:28:04,636 - DEBUG - server lang: <none>
2025-07-09 02:28:04,636 - DEBUG - kex follows: False
2025-07-09 02:28:04,636 - DEBUG - === Key exchange agreements ===
2025-07-09 02:28:04,636 - DEBUG - Kex: <EMAIL>
2025-07-09 02:28:04,636 - DEBUG - HostKey: ssh-ed25519
2025-07-09 02:28:04,636 - DEBUG - Cipher: aes128-ctr
2025-07-09 02:28:04,636 - DEBUG - MAC: hmac-sha2-256
2025-07-09 02:28:04,637 - DEBUG - Compression: none
2025-07-09 02:28:04,637 - DEBUG - === End of kex handshake ===
2025-07-09 02:28:04,709 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 02:28:04,710 - DEBUG - Switch to new keys ...
2025-07-09 02:28:04,711 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 02:28:04,711 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 02:28:04,792 - DEBUG - userauth is OK
2025-07-09 02:28:04,815 - INFO - Authentication (password) successful!
2025-07-09 02:28:04,815 - INFO - SSH connection established successfully
2025-07-09 02:28:04,815 - DEBUG - Executing command: echo 'Hello, World!'
2025-07-09 02:28:04,815 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 02:28:04,914 - DEBUG - Received global request "<EMAIL>"
2025-07-09 02:28:04,915 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 02:28:04,980 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 02:28:04,980 - DEBUG - Secsh channel 0 opened.
2025-07-09 02:28:05,070 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 02:28:05,130 - DEBUG - [chan 0] EOF received (0)
2025-07-09 02:28:05,130 - DEBUG - Command completed successfully (status: 0)
2025-07-09 02:28:05,132 - DEBUG - [chan 0] EOF sent (0)
2025-07-09 02:28:05,132 - DEBUG - Command output: Hello, World!
2025-07-09 02:28:05,133 - DEBUG - EOF in transport thread
2025-07-09 02:30:06,742 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 02:30:06,742 - DEBUG - Using password authentication
2025-07-09 02:30:06,742 - DEBUG - Connecting with parameters: {'hostname': 'jtmksrv', 'port': 656, 'username': 'user', 'timeout': 10, 'banner_timeout': 60, 'auth_timeout': 30, 'allow_agent': False, 'look_for_keys': False, 'password': '***'}
2025-07-09 02:30:09,500 - DEBUG - starting thread (client mode): 0x82c85df0
2025-07-09 02:30:09,500 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 02:30:09,525 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 02:30:09,525 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 02:30:09,546 - DEBUG - === Key exchange possibilities ===
2025-07-09 02:30:09,546 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 02:30:09,546 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 02:30:09,546 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:30:09,546 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:30:09,546 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:30:09,547 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:30:09,547 - DEBUG - client compress: none, <EMAIL>
2025-07-09 02:30:09,547 - DEBUG - server compress: none, <EMAIL>
2025-07-09 02:30:09,547 - DEBUG - client lang: <none>
2025-07-09 02:30:09,548 - DEBUG - server lang: <none>
2025-07-09 02:30:09,548 - DEBUG - kex follows: False
2025-07-09 02:30:09,548 - DEBUG - === Key exchange agreements ===
2025-07-09 02:30:09,548 - DEBUG - Kex: <EMAIL>
2025-07-09 02:30:09,548 - DEBUG - HostKey: ssh-ed25519
2025-07-09 02:30:09,548 - DEBUG - Cipher: aes128-ctr
2025-07-09 02:30:09,548 - DEBUG - MAC: hmac-sha2-256
2025-07-09 02:30:09,548 - DEBUG - Compression: none
2025-07-09 02:30:09,548 - DEBUG - === End of kex handshake ===
2025-07-09 02:30:09,574 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 02:30:09,575 - DEBUG - Switch to new keys ...
2025-07-09 02:30:09,575 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 02:30:09,575 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 02:30:09,663 - DEBUG - userauth is OK
2025-07-09 02:30:09,687 - INFO - Authentication (password) successful!
2025-07-09 02:30:09,687 - INFO - SSH connection established successfully
2025-07-09 02:30:09,687 - DEBUG - Executing command: echo 'Hello, World!'
2025-07-09 02:30:09,688 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 02:30:09,788 - DEBUG - Received global request "<EMAIL>"
2025-07-09 02:30:09,788 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 02:30:09,864 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 02:30:09,865 - DEBUG - Secsh channel 0 opened.
2025-07-09 02:30:09,951 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 02:30:10,021 - DEBUG - [chan 0] EOF received (0)
2025-07-09 02:30:10,022 - DEBUG - Command completed successfully (status: 0)
2025-07-09 02:30:10,022 - DEBUG - [chan 0] EOF sent (0)
2025-07-09 02:30:10,022 - DEBUG - Command output: Hello, World!
2025-07-09 02:30:10,023 - DEBUG - EOF in transport thread
2025-07-09 02:30:30,295 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 02:30:30,295 - DEBUG - Using password authentication
2025-07-09 02:30:30,296 - DEBUG - Connecting with parameters: {'hostname': 'jtmksrv', 'port': 656, 'username': 'user', 'timeout': 10, 'banner_timeout': 60, 'auth_timeout': 30, 'allow_agent': False, 'look_for_keys': False, 'password': '***'}
2025-07-09 02:30:30,356 - DEBUG - starting thread (client mode): 0x3c319100
2025-07-09 02:30:30,356 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 02:30:30,379 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 02:30:30,379 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 02:30:30,401 - DEBUG - === Key exchange possibilities ===
2025-07-09 02:30:30,402 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 02:30:30,402 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 02:30:30,402 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:30:30,402 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:30:30,402 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:30:30,402 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:30:30,403 - DEBUG - client compress: none, <EMAIL>
2025-07-09 02:30:30,403 - DEBUG - server compress: none, <EMAIL>
2025-07-09 02:30:30,403 - DEBUG - client lang: <none>
2025-07-09 02:30:30,404 - DEBUG - server lang: <none>
2025-07-09 02:30:30,404 - DEBUG - kex follows: False
2025-07-09 02:30:30,404 - DEBUG - === Key exchange agreements ===
2025-07-09 02:30:30,404 - DEBUG - Kex: <EMAIL>
2025-07-09 02:30:30,404 - DEBUG - HostKey: ssh-ed25519
2025-07-09 02:30:30,404 - DEBUG - Cipher: aes128-ctr
2025-07-09 02:30:30,404 - DEBUG - MAC: hmac-sha2-256
2025-07-09 02:30:30,404 - DEBUG - Compression: none
2025-07-09 02:30:30,405 - DEBUG - === End of kex handshake ===
2025-07-09 02:30:30,430 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 02:30:30,430 - DEBUG - Switch to new keys ...
2025-07-09 02:30:30,430 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 02:30:30,430 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 02:30:30,507 - DEBUG - userauth is OK
2025-07-09 02:30:30,529 - INFO - Authentication (password) successful!
2025-07-09 02:30:30,529 - INFO - SSH connection established successfully
2025-07-09 02:30:30,530 - DEBUG - Executing command: echo 'Hello, World!'
2025-07-09 02:30:30,530 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 02:30:30,625 - DEBUG - Received global request "<EMAIL>"
2025-07-09 02:30:30,625 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 02:30:30,700 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 02:30:30,700 - DEBUG - Secsh channel 0 opened.
2025-07-09 02:30:30,783 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 02:30:30,855 - DEBUG - [chan 0] EOF received (0)
2025-07-09 02:30:30,855 - DEBUG - Command completed successfully (status: 0)
2025-07-09 02:30:30,856 - DEBUG - [chan 0] EOF sent (0)
2025-07-09 02:30:30,856 - DEBUG - Command output: Hello, World!
2025-07-09 02:30:30,856 - DEBUG - EOF in transport thread
2025-07-09 02:30:39,303 - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 02:30:39,303 - DEBUG - Using password authentication
2025-07-09 02:30:39,303 - DEBUG - Connecting with parameters: {'hostname': 'jtmksrv', 'port': 656, 'username': 'user', 'timeout': 10, 'banner_timeout': 60, 'auth_timeout': 30, 'allow_agent': False, 'look_for_keys': False, 'password': '***'}
2025-07-09 02:30:39,351 - DEBUG - starting thread (client mode): 0x62743830
2025-07-09 02:30:39,351 - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 02:30:39,356 - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 02:30:39,357 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 02:30:39,416 - DEBUG - === Key exchange possibilities ===
2025-07-09 02:30:39,416 - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 02:30:39,416 - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 02:30:39,417 - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:30:39,417 - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:30:39,417 - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:30:39,417 - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:30:39,417 - DEBUG - client compress: none, <EMAIL>
2025-07-09 02:30:39,417 - DEBUG - server compress: none, <EMAIL>
2025-07-09 02:30:39,418 - DEBUG - client lang: <none>
2025-07-09 02:30:39,418 - DEBUG - server lang: <none>
2025-07-09 02:30:39,418 - DEBUG - kex follows: False
2025-07-09 02:30:39,418 - DEBUG - === Key exchange agreements ===
2025-07-09 02:30:39,418 - DEBUG - Kex: <EMAIL>
2025-07-09 02:30:39,419 - DEBUG - HostKey: ssh-ed25519
2025-07-09 02:30:39,419 - DEBUG - Cipher: aes128-ctr
2025-07-09 02:30:39,419 - DEBUG - MAC: hmac-sha2-256
2025-07-09 02:30:39,419 - DEBUG - Compression: none
2025-07-09 02:30:39,419 - DEBUG - === End of kex handshake ===
2025-07-09 02:30:39,517 - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 02:30:39,518 - DEBUG - Switch to new keys ...
2025-07-09 02:30:39,518 - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 02:30:39,518 - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 02:30:39,627 - DEBUG - userauth is OK
2025-07-09 02:30:39,648 - INFO - Authentication (password) successful!
2025-07-09 02:30:39,648 - INFO - SSH connection established successfully
2025-07-09 02:30:39,650 - DEBUG - Executing command: echo 'Hello, World!'
2025-07-09 02:30:39,650 - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 02:30:39,748 - DEBUG - Received global request "<EMAIL>"
2025-07-09 02:30:39,748 - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 02:30:39,844 - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 02:30:39,845 - DEBUG - Secsh channel 0 opened.
2025-07-09 02:30:39,935 - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 02:30:40,012 - DEBUG - [chan 0] EOF received (0)
2025-07-09 02:30:40,013 - DEBUG - Command completed successfully (status: 0)
2025-07-09 02:30:40,013 - DEBUG - [chan 0] EOF sent (0)
2025-07-09 02:30:40,013 - DEBUG - Command output: Hello, World!
2025-07-09 02:30:40,014 - DEBUG - EOF in transport thread
