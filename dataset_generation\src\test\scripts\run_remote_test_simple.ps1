# Parameters
$remoteUser = "user"
$remoteHost = "jtmksrv"
$remotePort = 656
$remoteDir = "/home/<USER>/dataset/test_run"

# Commands to run on the remote server
$commands = @(
    "cd $remoteDir",
    "source venv/bin/activate",
    "python -m pytest src/test/ -v"
) -join "; "

# Build the SSH command
$sshCommand = "ssh -p $remotePort ${remoteUser}@${remoteHost} `"$commands`""

# Execute the command
Write-Host "Running tests on remote server..."
Write-Host "Command: $sshCommand"

Invoke-Expression $sshCommand
