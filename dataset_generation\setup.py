from setuptools import setup, find_packages

setup(
    name="adversarial-ddos-sdn-dataset",
    version="0.1.0",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    install_requires=[
        "mininet",
        "ryu",
        "scapy",
        "pandas",
        "numpy",
        "paramiko",
        "pytest",
    ],
    python_requires=">=3.7",
    entry_points={
        "console_scripts": [
            "ddos-dataset=main_simple:main",
            "ddos-test=test:run_tests",
        ],
    },
    author="Your Name",
    author_email="<EMAIL>",
    description="Adversarial DDoS Attacks SDN Dataset Generator",
    long_description=open("README.md").read(),
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/adversarial-ddos-sdn-dataset",
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
)
