2025-07-08 22:05:36,738 - INFO - Starting remote test with detailed logging...
2025-07-08 22:05:36,738 - INFO - Connecting to user@jtmksrv:656
2025-07-08 22:05:36,790 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-08 22:05:37,001 - INFO - Authentication (password) successful!
2025-07-08 22:05:37,189 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-08 22:05:37,190 - INFO - SSH connection established
2025-07-08 22:05:37,190 - INFO - Executing: mkdir -p \home\user\dataset\test_run
2025-07-08 22:05:37,301 - INFO - Executing: mkdir -p /home/<USER>/dataset/test_run/controller
2025-07-08 22:05:37,481 - INFO - Executing: mkdir -p /home/<USER>/dataset/test_run/attacks
2025-07-08 22:05:37,669 - <PERSON>FO - Uploading simple_test.py to /home/<USER>/dataset/test_run/simple_test.py
2025-07-08 22:05:37,834 - INFO - Uploaded simple_test.py successfully
2025-07-08 22:05:37,835 - WARNING - Local file not found: main.py
2025-07-08 22:05:37,836 - WARNING - Local file not found: config.json
2025-07-08 22:05:37,836 - WARNING - Local file not found: topology.py
2025-07-08 22:05:37,836 - WARNING - Local file not found: controller\ryu_controller_app.py
2025-07-08 22:05:37,837 - WARNING - Local file not found: attacks\gen_syn_flood.py
2025-07-08 22:05:37,838 - WARNING - Local file not found: attacks\gen_udp_flood.py
2025-07-08 22:05:37,838 - WARNING - Local file not found: attacks\gen_advanced_adversarial_ddos_attacks.py
2025-07-08 22:05:37,840 - INFO - Uploading run_test_with_logging.py to /home/<USER>/dataset/test_run/run_test_with_logging.py
2025-07-08 22:05:37,955 - INFO - Uploaded run_test_with_logging.py successfully
2025-07-08 22:05:37,955 - INFO - Running test with detailed logging...
2025-07-08 22:05:37,955 - INFO - Executing: cd \home\user\dataset\test_run && sudo python3 /home/<USER>/dataset/test_run/run_test_with_logging.py
