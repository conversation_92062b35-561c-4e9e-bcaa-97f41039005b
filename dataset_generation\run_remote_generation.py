#!/usr/bin/env python3
"""
Script to run dataset generation on a remote server.
"""
import sys
import os
import time
from pathlib import Path
import zipfile

# Add parent directory to path to import remote_exec
sys.path.append(str(Path(__file__).parent))
from src.remote.remote_exec import RemoteCL<PERSON>

def zip_project(path, zip_handle):
    for root, dirs, files in os.walk(path):
        for file in files:
            zip_handle.write(os.path.join(root, file), os.path.relpath(os.path.join(root, file), os.path.join(path, '..')))

def run_remote_generation():
    """Run dataset generation on remote server."""
    # Remote server configuration
    hostname = 'jtmksrv'  # From test_remote_connection.py
    port = 656
    username = 'user'     # From test_remote_connection.py
    password = '1'        # From test_remote_connection.py
    
    # Remote paths (adjust these based on your server setup)
    remote_working_dir = '~/dataset_generation'  # Update this path
    remote_script = 'main.py'  # Or the script that runs the dataset generation
    
    print(f"Connecting to {username}@{hostname}:{port}...")
    
    # Create and connect to the remote client
    client = RemoteCLI(
        hostname=hostname,
        port=port,
        username=username,
        password=password
    )
    
    if not client.connect():
        print("Failed to connect to remote server.")
        return 1
    
    try:
        print("Connected to remote server.")

        # Get the user's home directory
        stdout, _, _ = client.execute_command('echo $HOME')
        remote_home = stdout.strip()

        # Zip the project directory
        project_path = os.path.dirname(os.path.abspath(__file__))
        zip_file_name = 'project.zip'
        zip_file_path = os.path.join(project_path, zip_file_name)
        with zipfile.ZipFile(zip_file_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            zip_project(project_path, zipf)

        # Upload the zipped project
        remote_zip_path = f'{remote_home}/project.zip'
        print(f"Uploading {zip_file_name} to {remote_zip_path}")
        client.upload_file(zip_file_path, remote_zip_path)

        # Unzip the project on the remote server
        print(f"Unzipping {zip_file_name} on the remote server")
        client.execute_command(f'unzip -o {remote_zip_path} -d {remote_home}/dataset_generation')
        print(f"Listing contents of {remote_home}/dataset_generation:")
        client.execute_command(f'ls -R {remote_home}/dataset_generation')
        
        # Create the remote directory if it doesn't exist
        print(f"Creating remote directory: {remote_working_dir}")
        client.execute_command(f'mkdir -p {remote_working_dir}')

        # Check Python version on remote server

        # Clean up any previous Mininet instances
        print("Cleaning up Mininet...")
        client.execute_command('sudo mn -c', sudo=True)

        # Check Python version on remote server
        print("Checking Python version on remote server...")
        stdout, stderr, exit_code = client.execute_command('python3 --version')
        print(f"Remote Python version: {stdout.strip()}")
        if exit_code != 0:
            print("Could not determine remote Python version. Please ensure python3 is installed.")
            return 1

        # Run the dataset generation script
        print("Starting dataset generation...")
        command = f'sudo python3 {remote_home}/dataset_generation/dataset_generation/main.py'
        print(f"Executing: {command}")
        
        # Execute the command with a timeout of 1 hour (3600 seconds)
        stdout, stderr, exit_code = client.execute_command(
            command=command,
            sudo=True, # Added sudo=True
            get_pty=True,  # Required for long-running processes
            timeout=3600    # 1 hour timeout
        )
        
        print(f"\n=== Command Output ===")
        print(stdout)
        if stderr:
            print(f"\n=== Errors ===")
            print(stderr)
        
        print(f"\nExit code: {exit_code}")
        
        if exit_code == 0:
            print("\n✅ Dataset generation completed successfully!")
            
            # Download generated datasets and logs
            print("Downloading generated files...")
            remote_pcap = f'{remote_home}/dataset_generation/test_traffic.pcap'
            local_pcap = './data/test_traffic.pcap'
            client.download_file(remote_pcap, local_pcap)
            print(f"Downloaded {remote_pcap} to {local_pcap}")

            remote_csv = f'{remote_home}/dataset_generation/test_features.csv'
            local_csv = './data/test_features.csv'
            client.download_file(remote_csv, local_csv)
            print(f"Downloaded {remote_csv} to {local_csv}")

            remote_ryu_log = f'{remote_home}/dataset_generation/ryu_controller.log'
            local_ryu_log = './logs/ryu_controller.log'
            client.download_file(remote_ryu_log, local_ryu_log)
            print(f"Downloaded {remote_ryu_log} to {local_ryu_log}")

            remote_dumpcap_log = f'{remote_home}/dataset_generation/dumpcap.log'
            local_dumpcap_log = './logs/dumpcap.log'
            client.download_file(remote_dumpcap_log, local_dumpcap_log)
            print(f"Downloaded {remote_dumpcap_log} to {local_dumpcap_log}")
            
        else:
            print("\n❌ Dataset generation failed!")
            
        return exit_code
        
    except Exception as e:
        print(f"An error occurred: {str(e)}")
        return 1
    finally:
        print("Closing connection...")
        client.close()

if __name__ == "__main__":
    sys.exit(run_remote_generation())
