#!/usr/bin/env python3
import sys
import os
import logging
import paramiko
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ssh_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_ssh_connection(hostname, port, username, password):
    """Test SSH connection with paramiko."""
    logger.info(f"Testing SSH connection to {username}@{hostname}:{port}")
    
    client = paramiko.SSHClient()
    client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        logger.info("Attempting to connect...")
        client.connect(
            hostname=hostname,
            port=port,
            username=username,
            password=password,
            timeout=10
        )
        logger.info("SSH connection successful!")
        
        # Test command execution
        logger.info("Testing command execution...")
        stdin, stdout, stderr = client.exec_command('echo "Hello from $(hostname)"')
        output = stdout.read().decode().strip()
        error = stderr.read().decode().strip()
        
        if output:
            logger.info(f"Command output: {output}")
        if error:
            logger.error(f"Command error: {error}")
            
        return True
        
    except Exception as e:
        logger.error(f"SSH connection failed: {e}")
        return False
    finally:
        client.close()
        logger.info("SSH connection closed.")

if __name__ == "__main__":
    # SSH connection details
    HOSTNAME = 'jtmksrv'
    PORT = 656
    USERNAME = 'user'
    PASSWORD = '1'  # In production, use a more secure method to handle passwords
    
    logger.info("Starting SSH test...")
    success = test_ssh_connection(HOSTNAME, PORT, USERNAME, PASSWORD)
    
    if success:
        logger.info("✓ SSH test completed successfully!")
        sys.exit(0)
    else:
        logger.error("✗ SSH test failed!")
        sys.exit(1)
