"""
Pytest configuration and fixtures for the test suite.
"""
import os
import sys
import logging
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Configure logging for tests
def pytest_configure(config):
    """Configure pytest and logging."""
    # Set up logging to file
    log_dir = Path('test_logs')
    log_dir.mkdir(exist_ok=True)
    
    # Configure root logger
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / 'test.log'),
            logging.StreamHandler()
        ]
    )
    
    # Set higher log level for external libraries
    logging.getLogger('paramiko').setLevel(logging.WARNING)
    logging.getLogger('matplotlib').setLevel(logging.WARNING)

# Fixtures can be added here
