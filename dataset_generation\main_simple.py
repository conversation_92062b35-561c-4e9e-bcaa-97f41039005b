#!/usr/bin/env python3
"""
Simplified main entry point for the Adversarial DDoS Attacks SDN Dataset Generator.
"""
import sys
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('app.log')
    ]
)

def main():
    """Main function to run the dataset generator."""
    try:
        # Add src to Python path
        sys.path.append(str(Path(__file__).parent / "src"))
        
        from core.generator import DatasetGenerator
        from config import load_config
        
        logger = logging.getLogger(__name__)
        logger.info("Starting Adversarial DDoS Attacks SDN Dataset Generator")
        
        # Load configuration
        config = load_config()
        
        # Initialize and run the dataset generator
        generator = DatasetGenerator(config)
        generator.run()
        
        logger.info("Dataset generation completed successfully")
        return 0
    except Exception as e:
        logger.error(f"An error occurred: {str(e)}", exc_info=True)
        return 1

if __name__ == "__main__":
    sys.exit(main())
