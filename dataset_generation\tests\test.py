#!/usr/bin/env python3
"""
Simplified test runner for the Adversarial DDoS Attacks SDN Dataset.
"""
import sys
import unittest
import logging
from pathlib import Path

def run_tests():
    """Run all tests."""
    # Add src to Python path
    sys.path.append(str(Path(__file__).parent / "src"))
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test.log')
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("Starting test suite")
    
    # Discover and run all tests
    test_loader = unittest.TestLoader()
    test_suite = test_loader.discover('src/tests', pattern='test_*.py')
    
    # Run the tests
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)
    
    # Return appropriate exit code
    return 0 if result.wasSuccessful() else 1

if __name__ == "__main__":
    sys.exit(run_tests())
