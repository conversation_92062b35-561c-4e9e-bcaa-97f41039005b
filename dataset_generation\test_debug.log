2025-07-09 02:14:14,501 - __main__ - ERROR - Test failed: No module named 'remote_exec'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\InSDN_ddos_dataset\adversarial-ddos-attacks-sdn-dataset\dataset_generation\run_test.py", line 21, in <module>
    from src.test.integration.test_remote_exec import TestRemoteCLI
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Desktop\InSDN_ddos_dataset\adversarial-ddos-attacks-sdn-dataset\dataset_generation\src\test\integration\test_remote_exec.py", line 10, in <module>
    from remote_exec import RemoteCLI
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'remote_exec'
2025-07-09 02:14:37,410 - __main__ - ERROR - Test failed: No module named 'remote_exec'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\InSDN_ddos_dataset\adversarial-ddos-attacks-sdn-dataset\dataset_generation\run_test.py", line 21, in <module>
    from src.test.integration.test_remote_exec import TestRemoteCLI
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Desktop\InSDN_ddos_dataset\adversarial-ddos-attacks-sdn-dataset\dataset_generation\src\test\integration\test_remote_exec.py", line 10, in <module>
    from remote_exec import RemoteCLI
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'remote_exec'
2025-07-09 02:15:15,568 - __main__ - INFO - Starting test...
2025-07-09 02:15:15,571 - src.remote_exec - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 02:15:15,572 - src.remote_exec - DEBUG - Using password authentication
2025-07-09 02:15:15,573 - src.remote_exec - DEBUG - Connecting with parameters: {'hostname': 'jtmksrv', 'port': 656, 'username': 'user', 'timeout': 10, 'banner_timeout': 60, 'auth_timeout': 30, 'allow_agent': False, 'look_for_keys': False, 'password': '***'}
2025-07-09 02:15:15,708 - paramiko.transport - DEBUG - starting thread (client mode): 0x4bf85d90
2025-07-09 02:15:15,709 - paramiko.transport - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 02:15:15,733 - paramiko.transport - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 02:15:15,733 - paramiko.transport - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 02:15:15,754 - paramiko.transport - DEBUG - === Key exchange possibilities ===
2025-07-09 02:15:15,754 - paramiko.transport - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 02:15:15,754 - paramiko.transport - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 02:15:15,754 - paramiko.transport - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:15:15,754 - paramiko.transport - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:15:15,755 - paramiko.transport - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:15:15,755 - paramiko.transport - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:15:15,755 - paramiko.transport - DEBUG - client compress: none, <EMAIL>
2025-07-09 02:15:15,755 - paramiko.transport - DEBUG - server compress: none, <EMAIL>
2025-07-09 02:15:15,755 - paramiko.transport - DEBUG - client lang: <none>
2025-07-09 02:15:15,755 - paramiko.transport - DEBUG - server lang: <none>
2025-07-09 02:15:15,756 - paramiko.transport - DEBUG - kex follows: False
2025-07-09 02:15:15,756 - paramiko.transport - DEBUG - === Key exchange agreements ===
2025-07-09 02:15:15,756 - paramiko.transport - DEBUG - Kex: <EMAIL>
2025-07-09 02:15:15,756 - paramiko.transport - DEBUG - HostKey: ssh-ed25519
2025-07-09 02:15:15,756 - paramiko.transport - DEBUG - Cipher: aes128-ctr
2025-07-09 02:15:15,756 - paramiko.transport - DEBUG - MAC: hmac-sha2-256
2025-07-09 02:15:15,757 - paramiko.transport - DEBUG - Compression: none
2025-07-09 02:15:15,757 - paramiko.transport - DEBUG - === End of kex handshake ===
2025-07-09 02:15:15,784 - paramiko.transport - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 02:15:15,785 - paramiko.transport - DEBUG - Switch to new keys ...
2025-07-09 02:15:15,785 - paramiko.transport - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 02:15:15,785 - paramiko.transport - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 02:15:15,870 - paramiko.transport - DEBUG - userauth is OK
2025-07-09 02:15:15,893 - paramiko.transport - INFO - Authentication (password) successful!
2025-07-09 02:15:15,893 - src.remote_exec - INFO - SSH connection established successfully
2025-07-09 02:15:15,893 - src.remote_exec - DEBUG - Executing command: echo 'Hello, World!'
2025-07-09 02:15:15,893 - paramiko.transport - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 02:15:15,985 - paramiko.transport - DEBUG - Received global request "<EMAIL>"
2025-07-09 02:15:15,985 - paramiko.transport - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 02:15:16,058 - paramiko.transport - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 02:15:16,058 - paramiko.transport - DEBUG - Secsh channel 0 opened.
2025-07-09 02:15:16,143 - paramiko.transport - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 02:15:16,209 - paramiko.transport - DEBUG - [chan 0] EOF received (0)
2025-07-09 02:15:16,209 - src.remote_exec - DEBUG - Command completed successfully (status: 0)
2025-07-09 02:15:16,210 - paramiko.transport - DEBUG - [chan 0] EOF sent (0)
2025-07-09 02:15:16,210 - src.remote_exec - DEBUG - Command output: Hello, World!
2025-07-09 02:15:16,210 - paramiko.transport - DEBUG - EOF in transport thread
2025-07-09 02:15:16,210 - __main__ - INFO - Test completed successfully
2025-07-09 02:17:46,882 - __main__ - INFO - Starting test...
2025-07-09 02:17:46,882 - src.remote.remote_exec - INFO - Attempting to connect to user@jtmksrv:656
2025-07-09 02:17:46,882 - src.remote.remote_exec - DEBUG - Using password authentication
2025-07-09 02:17:46,882 - src.remote.remote_exec - DEBUG - Connecting with parameters: {'hostname': 'jtmksrv', 'port': 656, 'username': 'user', 'timeout': 10, 'banner_timeout': 60, 'auth_timeout': 30, 'allow_agent': False, 'look_for_keys': False, 'password': '***'}
2025-07-09 02:17:47,383 - paramiko.transport - DEBUG - starting thread (client mode): 0x53f1e690
2025-07-09 02:17:47,383 - paramiko.transport - DEBUG - Local version/idstring: SSH-2.0-paramiko_3.5.1
2025-07-09 02:17:47,414 - paramiko.transport - DEBUG - Remote version/idstring: SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.7
2025-07-09 02:17:47,414 - paramiko.transport - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-09 02:17:47,435 - paramiko.transport - DEBUG - === Key exchange possibilities ===
2025-07-09 02:17:47,435 - paramiko.transport - DEBUG - kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, diffie-hellman-group14-sha256, diffie-hellman-group14-sha1
2025-07-09 02:17:47,436 - paramiko.transport - DEBUG - server key: ssh-rsa, rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519
2025-07-09 02:17:47,436 - paramiko.transport - DEBUG - client encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:17:47,436 - paramiko.transport - DEBUG - server encrypt: <EMAIL>, aes128-ctr, aes192-ctr, aes256-ctr, <EMAIL>, <EMAIL>
2025-07-09 02:17:47,436 - paramiko.transport - DEBUG - client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:17:47,436 - paramiko.transport - DEBUG - server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha2-512, hmac-sha1
2025-07-09 02:17:47,436 - paramiko.transport - DEBUG - client compress: none, <EMAIL>
2025-07-09 02:17:47,437 - paramiko.transport - DEBUG - server compress: none, <EMAIL>
2025-07-09 02:17:47,437 - paramiko.transport - DEBUG - client lang: <none>
2025-07-09 02:17:47,437 - paramiko.transport - DEBUG - server lang: <none>
2025-07-09 02:17:47,437 - paramiko.transport - DEBUG - kex follows: False
2025-07-09 02:17:47,437 - paramiko.transport - DEBUG - === Key exchange agreements ===
2025-07-09 02:17:47,437 - paramiko.transport - DEBUG - Kex: <EMAIL>
2025-07-09 02:17:47,437 - paramiko.transport - DEBUG - HostKey: ssh-ed25519
2025-07-09 02:17:47,438 - paramiko.transport - DEBUG - Cipher: aes128-ctr
2025-07-09 02:17:47,438 - paramiko.transport - DEBUG - MAC: hmac-sha2-256
2025-07-09 02:17:47,438 - paramiko.transport - DEBUG - Compression: none
2025-07-09 02:17:47,438 - paramiko.transport - DEBUG - === End of kex handshake ===
2025-07-09 02:17:47,464 - paramiko.transport - DEBUG - kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
2025-07-09 02:17:47,464 - paramiko.transport - DEBUG - Switch to new keys ...
2025-07-09 02:17:47,464 - paramiko.transport - DEBUG - Adding ssh-ed25519 host key for [jtmksrv]:656: b'89611e3b9a4264fe5eb2f9b75c6485e2'
2025-07-09 02:17:47,464 - paramiko.transport - DEBUG - Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521'}
2025-07-09 02:17:47,546 - paramiko.transport - DEBUG - userauth is OK
2025-07-09 02:17:47,568 - paramiko.transport - INFO - Authentication (password) successful!
2025-07-09 02:17:47,568 - src.remote.remote_exec - INFO - SSH connection established successfully
2025-07-09 02:17:47,568 - src.remote.remote_exec - DEBUG - Executing command: echo 'Hello, World!'
2025-07-09 02:17:47,569 - paramiko.transport - DEBUG - [chan 0] Max packet in: 32768 bytes
2025-07-09 02:17:47,664 - paramiko.transport - DEBUG - Received global request "<EMAIL>"
2025-07-09 02:17:47,664 - paramiko.transport - DEBUG - Rejecting "<EMAIL>" global request from server.
2025-07-09 02:17:47,730 - paramiko.transport - DEBUG - [chan 0] Max packet out: 32768 bytes
2025-07-09 02:17:47,732 - paramiko.transport - DEBUG - Secsh channel 0 opened.
2025-07-09 02:17:47,817 - paramiko.transport - DEBUG - [chan 0] Sesch channel 0 request ok
2025-07-09 02:17:47,885 - paramiko.transport - DEBUG - [chan 0] EOF received (0)
2025-07-09 02:17:47,885 - src.remote.remote_exec - DEBUG - Command completed successfully (status: 0)
2025-07-09 02:17:47,887 - paramiko.transport - DEBUG - [chan 0] EOF sent (0)
2025-07-09 02:17:47,887 - src.remote.remote_exec - DEBUG - Command output: Hello, World!
2025-07-09 02:17:47,887 - paramiko.transport - DEBUG - EOF in transport thread
2025-07-09 02:17:47,888 - __main__ - INFO - Test completed successfully
