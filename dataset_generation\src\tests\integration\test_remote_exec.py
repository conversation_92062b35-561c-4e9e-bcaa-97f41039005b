#!/usr/bin/env python3
import sys
import os
import unittest
from unittest.mock import patch, MagicMock
from pathlib import Path

# Add parent directory to path to import remote_exec
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))  # Add dataset_generation/src to path
from src.remote.remote_exec import RemoteCLI

class TestRemoteCLI(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        """Set up test environment"""
        cls.hostname = 'jtmksrv'  # Replace with actual test server
        cls.port = 656
        cls.username = 'user'     # Replace with test username
        cls.password = '1'     # Replace with test password or None for key auth
        
    def test_connection(self):
        """Test SSH connection"""
        print(f"\n{'='*50}")
        print(f"Testing connection to {self.username}@{self.hostname}:{self.port}")
        print(f"{'='*50}")
        
        client = RemoteCLI(
            hostname=self.hostname,
            port=self.port,
            username=self.username,
            password=self.password
        )
        
        # Test connection
        try:
            print("\n[1/3] Testing connection...")
            self.assertTrue(client.connect(), "Connection failed")
            self.assertIsNotNone(client.ssh, "SSH client not initialized")
            print("✓ Connection established successfully")
            
            # Test command execution
            print("\n[2/3] Testing command execution...")
            command = "echo 'Hello, World!'"
            print(f"Executing: {command}")
            
            # Execute the command - note that execute_command returns (stdin, stdout_str, stderr_str)
            stdin, stdout_str, stderr_str = client.execute_command(command)
            
            # Print the output for debugging
            print(f"STDOUT: {stdout_str}")
            if stderr_str:
                print(f"STDERR: {stderr_str}")
            
            # Check if the command was executed successfully
            self.assertIsNotNone(stdout_str, "Command execution failed - no output")
            self.assertNotIn("command not found", str(stdout_str).lower(), "Command not found on remote system")
            self.assertNotIn("permission denied", str(stdout_str).lower(), "Permission denied when executing command")
            
            # Check if we got the expected output
            self.assertIn("Hello, World!", stdout_str, "Unexpected command output")
            
            # If we got here, the command executed successfully
            print("✓ Command executed successfully")
            
        except Exception as e:
            print(f"\n❌ Test failed: {str(e)}")
            import traceback
            print("\nTraceback:")
            print(traceback.format_exc())
            self.fail(f"Test failed: {str(e)}")
            
        finally:
            print("\n[3/3] Cleaning up...")
            if hasattr(client, 'ssh') and client.ssh:
                client.close()
                print("✓ Connection closed properly")

class TestSimple(unittest.TestCase):
    def test_simple(self):
        """Simple test to verify test discovery"""
        print("\nRunning simple test...")
        self.assertTrue(True, "Simple test should pass")

if __name__ == "__main__":
    # Run tests
    unittest.main(argv=['first-arg-is-ignored'], exit=False)
