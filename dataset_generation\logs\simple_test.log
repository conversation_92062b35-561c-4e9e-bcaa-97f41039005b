2025-07-08 22:08:34,988 - INFO - Starting simple test...
2025-07-08 22:08:34,989 - INFO - Checking environment...
2025-07-08 22:08:34,989 - ERROR - Required file not found: C:\Users\<USER>\Desktop\InSDN_ddos_dataset\adversarial-ddos-attacks-sdn-dataset\dataset_generation\config.json
2025-07-08 22:08:34,989 - ERROR - Environment check failed. Please fix the issues and try again.
2025-07-08 22:08:56,685 - INFO - Starting simple test...
2025-07-08 22:08:56,685 - INFO - Checking environment...
2025-07-08 22:08:56,687 - INFO - Environment check passed
2025-07-08 22:08:56,688 - INFO - Created test configuration file: test_config.json
2025-07-08 22:08:56,691 - INFO - Test setup complete. You can now run the test with:
2025-07-08 22:08:56,691 - INFO - sudo python3 main.py test_config.json
2025-07-08 22:20:38,217 - INFO - Starting simple test...
2025-07-08 22:20:38,217 - INFO - Checking environment...
2025-07-08 22:20:38,218 - INFO - Environment check passed
2025-07-08 22:20:38,220 - INFO - Created test configuration file: test_config.json
2025-07-08 22:20:38,220 - INFO - Test setup complete. You can now run the test with:
2025-07-08 22:20:38,220 - INFO - sudo python3 main.py test_config.json
2025-07-08 22:28:09,510 - INFO - Starting simple test...
2025-07-08 22:28:09,510 - INFO - Checking environment...
2025-07-08 22:28:09,511 - INFO - Environment check passed
2025-07-08 22:28:09,511 - INFO - Created test configuration file: test_config.json
2025-07-08 22:28:09,512 - INFO - Test setup complete. You can now run the test with:
2025-07-08 22:28:09,512 - INFO - sudo python3 main.py test_config.json
2025-07-08 22:31:22,870 - INFO - Starting simple test...
2025-07-08 22:31:22,870 - INFO - Checking environment...
2025-07-08 22:31:22,871 - INFO - Environment check passed
2025-07-08 22:31:22,872 - INFO - Created test configuration file: test_config.json
2025-07-08 22:31:22,872 - INFO - Test setup complete. You can now run the test with:
2025-07-08 22:31:22,872 - INFO - sudo python3 main.py test_config.json
