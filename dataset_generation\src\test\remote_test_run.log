2025-07-08 20:27:38,357 - INFO - Starting remote test execution...
2025-07-08 20:27:38,357 - <PERSON>FO - Connecting to user@jtmksrv:656
2025-07-08 20:27:38,421 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-08 20:27:38,514 - INFO - Authentication (password) successful!
2025-07-08 20:27:38,681 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-08 20:27:38,681 - INFO - SSH connection established
2025-07-08 20:27:38,681 - INFO - Executing: mkdir -p \home\user\dataset\test_run
2025-07-08 20:27:38,774 - INFO - Executing: mkdir -p /home/<USER>/dataset/test_run/controller
2025-07-08 20:27:39,129 - INFO - Executing: mkdir -p /home/<USER>/dataset/test_run/attacks
2025-07-08 20:27:39,279 - INFO - Uploading simple_test.py to /home/<USER>/dataset/test_run/simple_test.py
2025-07-08 20:27:39,378 - INFO - Uploaded simple_test.py to /home/<USER>/dataset/test_run/simple_test.py
2025-07-08 20:27:39,379 - WARNING - Local file not found: main.py
2025-07-08 20:27:39,379 - WARNING - Local file not found: config.json
2025-07-08 20:27:39,379 - WARNING - Local file not found: topology.py
2025-07-08 20:27:39,379 - WARNING - Local file not found: controller\ryu_controller_app.py
2025-07-08 20:27:39,379 - WARNING - Local file not found: attacks\gen_syn_flood.py
2025-07-08 20:27:39,380 - WARNING - Local file not found: attacks\gen_udp_flood.py
2025-07-08 20:27:39,380 - WARNING - Local file not found: attacks\gen_advanced_adversarial_ddos_attacks.py
2025-07-08 20:27:39,380 - INFO - Running environment check...
2025-07-08 20:27:39,380 - INFO - Executing: cd /home/<USER>/dataset/test_run && python3 simple_test.py
2025-07-08 20:27:39,474 - INFO - Output: 2025-07-08 20:27:40,038 - INFO - Starting simple test...

2025-07-08 20:27:40,038 - INFO - Checking environment...

2025-07-08 20:27:40,038 - INFO - Environment check passed

2025-07-08 20:27:40,039 - INFO - Created test configuration file: test_config.json

2025-07-08 20:27:40,039 - INFO - Test setup complete. You can now run the test with:

2025-07-08 20:27:40,039 - INFO - sudo python3 main.py test_config.json
2025-07-08 20:27:39,475 - INFO - Installing dependencies...
2025-07-08 20:27:39,475 - INFO - Executing: echo 1 | sudo -S pip3 install mininet ryu scapy pandas
2025-07-08 20:27:46,696 - INFO - Output: [sudo] password for user: /usr/lib/python3/dist-packages/secretstorage/dhcrypto.py:15: CryptographyDeprecationWarning: Python 3.6 is no longer supported by the Python core team. Therefore, support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.6.

  from cryptography.utils import int_from_bytes

[33mWARNING: The directory '/home/<USER>/.cache/pip' or its parent directory is not owned or is not writable by the current user. The cache has been disabled. Check the permissions and owner of that directory. If executing pip with sudo, you should use sudo's -H flag.[0m

Requirement already satisfied: mininet in /usr/local/lib/python3.6/dist-packages/mininet-2.3.1b4-py3.6.egg (2.3.1b4)

Requirement already satisfied: ryu in /usr/local/lib/python3.6/dist-packages (4.34)

Requirement already satisfied: scapy in /usr/local/lib/python3.6/dist-packages (2.5.0)

Requirement already satisfied: pandas in /usr/local/lib/python3.6/dist-packages (1.1.5)

Requirement already satisfied: setuptools in /usr/lib/python3/dist-packages (from mininet) (39.0.1)

Requirement already satisfied: eventlet!=0.18.3,!=0.20.1,!=0.21.0,!=0.23.0,>=0.18.2 in /usr/local/lib/python3.6/dist-packages (from ryu) (0.30.2)

Requirement already satisfied: routes in ./.local/lib/python3.6/site-packages (from ryu) (2.5.1)

Requirement already satisfied: netaddr in ./.local/lib/python3.6/site-packages (from ryu) (0.10.1)

Requirement already satisfied: msgpack>=0.3.0 in ./.local/lib/python3.6/site-packages (from ryu) (1.0.5)

Requirement already satisfied: six>=1.4.0 in /usr/lib/python3/dist-packages (from ryu) (1.11.0)

Requirement already satisfied: ovs>=2.6.0 in ./.local/lib/python3.6/site-packages (from ryu) (3.5.0)

Requirement already satisfied: webob>=1.2 in ./.local/lib/python3.6/site-packages (from ryu) (1.8.9)

Requirement already satisfied: oslo.config>=2.5.0 in ./.local/lib/python3.6/site-packages (from ryu) (8.8.1)

Requirement already satisfied: tinyrpc in ./.local/lib/python3.6/site-packages (from ryu) (1.1.7)

Requirement already satisfied: numpy>=1.15.4 in /usr/local/lib/python3.6/dist-packages (from pandas) (1.19.5)

Requirement already satisfied: python-dateutil>=2.7.3 in /usr/local/lib/python3.6/dist-packages (from pandas) (2.9.0.post0)

Requirement already satisfied: pytz>=2017.2 in /usr/lib/python3/dist-packages (from pandas) (2018.3)

Requirement already satisfied: dnspython<2.0.0,>=1.15.0 in /usr/local/lib/python3.6/dist-packages (from eventlet!=0.18.3,!=0.20.1,!=0.21.0,!=0.23.0,>=0.18.2->ryu) (1.16.0)

Requirement already satisfied: greenlet>=0.3 in ./.local/lib/python3.6/site-packages (from eventlet!=0.18.3,!=0.20.1,!=0.21.0,!=0.23.0,>=0.18.2->ryu) (2.0.2)

Requirement already satisfied: requests>=2.18.0 in /usr/lib/python3/dist-packages (from oslo.config>=2.5.0->ryu) (2.18.4)

Requirement already satisfied: oslo.i18n>=3.15.3 in ./.local/lib/python3.6/site-packages (from oslo.config>=2.5.0->ryu) (5.1.0)

Requirement already satisfied: rfc3986>=1.2.0 in ./.local/lib/python3.6/site-packages (from oslo.config>=2.5.0->ryu) (1.5.0)

Requirement already satisfied: stevedore>=1.20.0 in ./.local/lib/python3.6/site-packages (from oslo.config>=2.5.0->ryu) (3.5.2)

Requirement already satisfied: importlib-metadata>=1.7.0 in ./.local/lib/python3.6/site-packages (from oslo.config>=2.5.0->ryu) (4.8.3)

Requirement already satisfied: debtcollector>=1.2.0 in ./.local/lib/python3.6/site-packages (from oslo.config>=2.5.0->ryu) (2.5.0)

Requirement already satisfied: PyYAML>=5.1 in ./.local/lib/python3.6/site-packages (from oslo.config>=2.5.0->ryu) (6.0.1)

Requirement already satisfied: importlib-resources in ./.local/lib/python3.6/site-packages (from netaddr->ryu) (5.4.0)

Requirement already satisfied: sortedcontainers in /usr/local/lib/python3.6/dist-packages (from ovs>=2.6.0->ryu) (2.4.0)

Requirement already satisfied: repoze.lru>=0.3 in ./.local/lib/python3.6/site-packages (from routes->ryu) (0.7)

Requirement already satisfied: wrapt>=1.7.0 in ./.local/lib/python3.6/site-packages (from debtcollector>=1.2.0->oslo.config>=2.5.0->ryu) (1.16.0)

Requirement already satisfied: zipp>=0.5 in ./.local/lib/python3.6/site-packages (from importlib-metadata>=1.7.0->oslo.config>=2.5.0->ryu) (3.6.0)

Requirement already satisfied: typing-extensions>=3.6.4 in ./.local/lib/python3.6/site-packages (from importlib-metadata>=1.7.0->oslo.config>=2.5.0->ryu) (4.1.1)

Requirement already satisfied: pbr!=2.1.0,>=2.0.0 in ./.local/lib/python3.6/site-packages (from oslo.i18n>=3.15.3->oslo.config>=2.5.0->ryu) (6.1.1)

[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv[0m
2025-07-08 20:27:46,696 - INFO - Executing: cd /home/<USER>/dataset/test_run && ls -la
2025-07-08 20:27:46,787 - INFO - Output: total 52

drwxrwxr-x 4 <USER> <GROUP>  4096 Jul  8 19:12 .

drwxrwxr-x 3 <USER> <GROUP>  4096 Jul  8 19:02 ..

drwxrwxr-x 2 <USER> <GROUP>  4096 Jul  8 19:02 attacks

-rw-rw-r-- 1 <USER> <GROUP>  2857 Jul  8 19:12 config.json

drwxrwxr-x 2 <USER> <GROUP>  4096 Jul  8 19:02 controller

-rw-rw-r-- 1 <USER> <GROUP> 10755 Jul  8 19:12 main.py

-rw-rw-r-- 1 <USER> <GROUP>  3141 Jul  8 19:12 run_test_with_logging.py

-rw-rw-r-- 1 <USER> <GROUP>   822 Jul  8 20:27 simple_test.log

-rw-rw-r-- 1 <USER> <GROUP>  3650 Jul  8 20:27 simple_test.py

-rw-rw-r-- 1 <USER> <GROUP>  1027 Jul  8 20:27 test_config.json

-rw-rw-r-- 1 <USER> <GROUP>   391 Jul  8 19:12 topology.py
2025-07-08 20:27:46,787 - INFO - Starting test...
2025-07-08 20:27:46,788 - INFO - Executing: cd /home/<USER>/dataset/test_run && sudo python3 main.py test_config.json
2025-07-08 21:39:47,949 - INFO - Starting remote test execution...
2025-07-08 21:39:47,949 - INFO - Connecting to user@jtmksrv:656
2025-07-08 21:39:48,010 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-08 21:39:48,109 - INFO - Authentication (password) successful!
2025-07-08 21:39:48,314 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-08 21:39:48,315 - INFO - SSH connection established
2025-07-08 21:39:48,315 - INFO - Executing: mkdir -p \home\user\dataset\test_run
2025-07-08 21:39:48,400 - INFO - Executing: mkdir -p /home/<USER>/dataset/test_run/controller
2025-07-08 21:39:48,505 - INFO - Executing: mkdir -p /home/<USER>/dataset/test_run/attacks
2025-07-08 21:39:48,609 - INFO - Uploading simple_test.py to /home/<USER>/dataset/test_run/simple_test.py
2025-07-08 21:39:48,702 - INFO - Uploaded simple_test.py to /home/<USER>/dataset/test_run/simple_test.py
2025-07-08 21:39:48,702 - WARNING - Local file not found: main.py
2025-07-08 21:39:48,702 - WARNING - Local file not found: config.json
2025-07-08 21:39:48,702 - WARNING - Local file not found: topology.py
2025-07-08 21:39:48,702 - WARNING - Local file not found: controller\ryu_controller_app.py
2025-07-08 21:39:48,702 - WARNING - Local file not found: attacks\gen_syn_flood.py
2025-07-08 21:39:48,702 - WARNING - Local file not found: attacks\gen_udp_flood.py
2025-07-08 21:39:48,703 - WARNING - Local file not found: attacks\gen_advanced_adversarial_ddos_attacks.py
2025-07-08 21:39:48,703 - INFO - Running environment check...
2025-07-08 21:39:48,703 - INFO - Executing: cd /home/<USER>/dataset/test_run && python3 simple_test.py
2025-07-08 21:39:48,821 - INFO - Output: 2025-07-08 21:39:49,443 - INFO - Starting simple test...

2025-07-08 21:39:49,443 - INFO - Checking environment...

2025-07-08 21:39:49,443 - ERROR - Required file not found: main.py

2025-07-08 21:39:49,443 - ERROR - Environment check failed. Please fix the issues and try again.
2025-07-08 21:39:48,821 - ERROR - Environment check failed
2025-07-08 22:05:35,301 - INFO - Starting remote test execution...
2025-07-08 22:05:35,302 - INFO - Connecting to user@jtmksrv:656
2025-07-08 22:05:35,352 - INFO - Connected (version 2.0, client OpenSSH_7.6p1)
2025-07-08 22:05:35,536 - INFO - Authentication (password) successful!
2025-07-08 22:05:35,723 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-08 22:05:35,723 - INFO - SSH connection established
2025-07-08 22:05:35,723 - INFO - Executing: mkdir -p \home\user\dataset\test_run
2025-07-08 22:05:35,831 - INFO - Executing: mkdir -p /home/<USER>/dataset/test_run/controller
2025-07-08 22:05:36,014 - INFO - Executing: mkdir -p /home/<USER>/dataset/test_run/attacks
2025-07-08 22:05:36,182 - INFO - Uploading simple_test.py to /home/<USER>/dataset/test_run/simple_test.py
2025-07-08 22:05:36,369 - INFO - Uploaded simple_test.py to /home/<USER>/dataset/test_run/simple_test.py
2025-07-08 22:05:36,369 - WARNING - Local file not found: main.py
2025-07-08 22:05:36,369 - WARNING - Local file not found: config.json
2025-07-08 22:05:36,369 - WARNING - Local file not found: topology.py
2025-07-08 22:05:36,370 - WARNING - Local file not found: controller\ryu_controller_app.py
2025-07-08 22:05:36,370 - WARNING - Local file not found: attacks\gen_syn_flood.py
2025-07-08 22:05:36,370 - WARNING - Local file not found: attacks\gen_udp_flood.py
2025-07-08 22:05:36,370 - WARNING - Local file not found: attacks\gen_advanced_adversarial_ddos_attacks.py
2025-07-08 22:05:36,370 - INFO - Running environment check...
2025-07-08 22:05:36,370 - INFO - Executing: cd /home/<USER>/dataset/test_run && python3 simple_test.py
2025-07-08 22:05:36,479 - INFO - Output: 2025-07-08 22:05:37,121 - INFO - Starting simple test...

2025-07-08 22:05:37,121 - INFO - Checking environment...

2025-07-08 22:05:37,121 - ERROR - Required file not found: main.py

2025-07-08 22:05:37,121 - ERROR - Environment check failed. Please fix the issues and try again.
2025-07-08 22:05:36,481 - ERROR - Environment check failed
