================================================================================
 TEST EXECUTION REPORT
================================================================================
Date: 2025-07-08 22:19:09
Total tests: 2
Passed: 1
Failed: 1
Success rate: 50.0%
Total duration: 0.19 seconds

---------------------------------------- DETAILED RESULTS ----------------------------------------
PASS | Simple Environment Check                 | 0.08s
FAIL | Run Unit Tests                           | 0.04s
    Error output:
      import _frozen_importlib # frozen
      import _imp # builtin
      import '_thread' # <class '_frozen_importlib.BuiltinImporter'>
      import '_warnings' # <class '_frozen_importlib.BuiltinImporter'>
      import '_weakref' # <class '_frozen_importlib.BuiltinImporter'>
      import 'winreg' # <class '_frozen_importlib.BuiltinImporter'>
      import '_io' # <class '_frozen_importlib.BuiltinImporter'>
      import 'marshal' # <class '_frozen_importlib.BuiltinImporter'>
      import 'nt' # <class '_frozen_importlib.BuiltinImporter'>
      import '_frozen_importlib_external' # <class '_frozen_importlib.FrozenImporter'>
      # installing zipimport hook
      import 'time' # <class '_frozen_importlib.BuiltinImporter'>
      import 'zipimport' # <class '_frozen_importlib.FrozenImporter'>
      # installed zipimport hook
      # C:\Users\<USER>\miniconda3\Lib\encodings\__pycache__\__init__.cpython-312.pyc matches C:\Users\<USER>\miniconda3\Lib\encodings\__init__.py
      # code object from 'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\__pycache__\\__init__.cpython-312.pyc'
      import '_codecs' # <class '_frozen_importlib.BuiltinImporter'>
      import 'codecs' # <class '_frozen_importlib.FrozenImporter'>
      # C:\Users\<USER>\miniconda3\Lib\encodings\__pycache__\aliases.cpython-312.pyc matches C:\Users\<USER>\miniconda3\Lib\encodings\aliases.py
      # code object from 'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\__pycache__\\aliases.cpython-312.pyc'
      import 'encodings.aliases' # <_frozen_importlib_external.SourceFileLoader object at 0x000001A67ADF8A70>
      import 'encodings' # <_frozen_importlib_external.SourceFileLoader object at 0x000001A67ADC6EA0>
      # C:\Users\<USER>\miniconda3\Lib\encodings\__pycache__\utf_8.cpython-312.pyc matches C:\Users\<USER>\miniconda3\Lib\encodings\utf_8.py
      # code object from 'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\__pycache__\\utf_8.cpython-312.pyc'
      import 'encodings.utf_8' # <_frozen_importlib_external.SourceFileLoader object at 0x000001A67ADFB2C0>
      # C:\Users\<USER>\miniconda3\Lib\encodings\__pycache__\cp1252.cpython-312.pyc matches C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py
      # code object from 'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\__pycache__\\cp1252.cpython-312.pyc'
      import 'encodings.cp1252' # <_frozen_importlib_external.SourceFileLoader object at 0x000001A67ADFB470>
      import '_signal' # <class '_frozen_importlib.BuiltinImporter'>
      import '_abc' # <class '_frozen_importlib.BuiltinImporter'>
      import 'abc' # <class '_frozen_importlib.FrozenImporter'>
      import 'io' # <class '_frozen_importlib.FrozenImporter'>
      import '_stat' # <class '_frozen_importlib.BuiltinImporter'>
      import 'stat' # <class '_frozen_importlib.FrozenImporter'>
      import '_collections_abc' # <class '_frozen_importlib.FrozenImporter'>
      import 'genericpath' # <class '_frozen_importlib.FrozenImporter'>
      import '_winapi' # <class '_frozen_importlib.BuiltinImporter'>
      import 'ntpath' # <class '_frozen_importlib.FrozenImporter'>
      import 'os' # <class '_frozen_importlib.FrozenImporter'>
      import '_sitebuiltins' # <class '_frozen_importlib.FrozenImporter'>
      Processing user site-packages
      Processing global site-packages
      Adding directory: 'C:\\Users\\<USER>\\miniconda3'
      Adding directory: 'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages'
      Processing .pth file: 'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\distutils-precedence.pth'
      # C:\Users\<USER>\miniconda3\Lib\site-packages\_distutils_hack\__pycache__\__init__.cpython-312.pyc matches C:\Users\<USER>\miniconda3\Lib\site-packages\_distutils_hack\__init__.py
      # code object from 'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\_distutils_hack\\__pycache__\\__init__.cpython-312.pyc'
      import '_distutils_hack' # <_frozen_importlib_external.SourceFileLoader object at 0x000001A67B097380>
      Processing .pth file: 'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\easy-install.pth'
      Processing .pth file: 'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pytest-cov.pth'
      Processing .pth file: 'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pywin32.pth'
      # C:\Users\<USER>\miniconda3\Lib\site-packages\win32\lib\__pycache__\pywin32_bootstrap.cpython-312.pyc matches C:\Users\<USER>\miniconda3\Lib\site-packages\win32\lib\pywin32_bootstrap.py
      # code object from 'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\win32\\lib\\__pycache__\\pywin32_bootstrap.cpython-312.pyc'
      # possible namespace for C:\Users\<USER>\miniconda3\Lib\site-packages\pywin32_system32
      import 'pywin32_system32' # <_frozen_importlib_external.NamespaceLoader object at 0x000001A67B0957C0>
      import 'pywin32_bootstrap' # <_frozen_importlib_external.SourceFileLoader object at 0x000001A67B0D1460>
      import 'site' # <class '_frozen_importlib.FrozenImporter'>
      Python 3.12.2 | packaged by Anaconda, Inc. | (main, Feb 27 2024, 17:28:07) [MSC v.1916 64 bit (AMD64)] on win32
      Type "help", "copyright", "credits" or "license" for more information.
      C:\Users\<USER>\miniconda3\python.exe: can't open file 'C:\\Users\\<USER>\\Desktop\\InSDN_ddos_dataset\\adversarial-ddos-attacks-sdn-dataset\\dataset_generation\\src\\test\\pytest': [Errno 2] No such file or directory
      # clear sys.path_importer_cache
      # clear sys.path_hooks
      # clear builtins._
      # clear sys.path
      # clear sys.argv
      # clear sys.ps1
      # clear sys.ps2
      # clear sys.last_exc
      # clear sys.last_type
      # clear sys.last_value
      # clear sys.last_traceback
      # clear sys.__interactivehook__
      # clear sys.meta_path
      # restore sys.stdin
      # restore sys.stdout
      # restore sys.stderr
      # cleanup[2] removing sys
      # cleanup[2] removing builtins
      # cleanup[2] removing _frozen_importlib
      # cleanup[2] removing _imp
      # cleanup[2] removing _thread
      # cleanup[2] removing _warnings
      # cleanup[2] removing _weakref
      # cleanup[2] removing winreg
      # cleanup[2] removing _io
      # cleanup[2] removing marshal
      # cleanup[2] removing nt
      # cleanup[2] removing _frozen_importlib_external
      # cleanup[2] removing time
      # cleanup[2] removing zipimport
      # destroy zipimport
      # cleanup[2] removing _codecs
      # cleanup[2] removing codecs
      # cleanup[2] removing encodings.aliases
      # cleanup[2] removing encodings
      # destroy encodings
      # cleanup[2] removing encodings.utf_8
      # cleanup[2] removing encodings.cp1252
      # cleanup[2] removing _signal
      # cleanup[2] removing _abc
      # cleanup[2] removing abc
      # cleanup[2] removing io
      # cleanup[2] removing __main__
      # destroy __main__
      # cleanup[2] removing _stat
      # cleanup[2] removing stat
      # cleanup[2] removing _collections_abc
      # destroy _collections_abc
      # cleanup[2] removing genericpath
      # cleanup[2] removing _winapi
      # cleanup[2] removing ntpath
      # cleanup[2] removing os.path
      # cleanup[2] removing os
      # cleanup[2] removing _sitebuiltins
      # cleanup[2] removing _distutils_hack
      # destroy _distutils_hack
      # cleanup[2] removing pywin32_system32
      # cleanup[2] removing pywin32_bootstrap
      # destroy pywin32_bootstrap
      # destroy pywin32_system32
      # cleanup[2] removing site
      # destroy site
      # destroy time
      # destroy _signal
      # destroy _abc
      # destroy _sitebuiltins
      # destroy io
      # destroy abc
      # destroy ntpath
      # destroy _stat
      # destroy genericpath
      # destroy stat
      # destroy _winapi
      # destroy os
      # cleanup[3] wiping encodings.cp1252
      # cleanup[3] wiping encodings.utf_8
      # cleanup[3] wiping encodings.aliases
      # cleanup[3] wiping codecs
      # cleanup[3] wiping _codecs
      # cleanup[3] wiping _frozen_importlib_external
      # cleanup[3] wiping nt
      # cleanup[3] wiping marshal
      # destroy marshal
      # cleanup[3] wiping _io
      # cleanup[3] wiping winreg
      # cleanup[3] wiping _weakref
      # cleanup[3] wiping _warnings
      # cleanup[3] wiping _thread
      # cleanup[3] wiping _imp
      # cleanup[3] wiping _frozen_importlib
      # destroy _warnings
      # destroy _weakref
      # destroy _frozen_importlib_external
      # destroy _imp
      # cleanup[3] wiping sys
      # cleanup[3] wiping builtins
      # destroy sys.monitoring
      # destroy _thread
      # destroy _io
      # destroy nt
      # destroy winreg
      # clear sys.meta_path
      # clear sys.modules
      # destroy _frozen_importlib
      # destroy codecs
      # destroy sys
      # destroy encodings.aliases
      # destroy encodings.utf_8
      # destroy encodings.cp1252
      # destroy _codecs
      # destroy builtins
      # clear sys.audit hooks
