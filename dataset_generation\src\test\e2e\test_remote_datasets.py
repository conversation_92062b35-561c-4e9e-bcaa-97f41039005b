#!/usr/bin/env python3
"""
Remote Dataset Verification Test

This script uses remote_exec.py to:
1. Connect to the remote server
2. Run the dataset generation
3. Verify the generated datasets
4. Download the results
"""
import os
import sys
import time
import logging
import argparse
import paramiko
import shutil
import json
import stat
import signal
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any, Union, Callable

# Add parent directory to path to allow importing from src
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Add parent directory to path to import remote_exec
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from remote_exec import RemoteCLI
except ImportError as e:
    print(f"Error importing RemoteCLI: {e}")
    print("Please ensure the remote_exec.py file exists in the parent directory")
    sys.exit(1)

# Parse command line arguments
parser = argparse.ArgumentParser(description='Test remote dataset generation')
parser.add_argument('--debug', action='store_true', help='Enable debug logging')
parser.add_argument('--no-download', action='store_true', help='Skip downloading files')
parser.add_argument('--cleanup', action='store_true', help='Clean up remote files after test')
args = parser.parse_args()

# Configure logging
def setup_logger(debug: bool = False) -> logging.Logger:
    """Set up logging configuration
    
    Args:
        debug: If True, set log level to DEBUG
        
    Returns:
        Configured logger instance
    """
    # Create logs directory if it doesn't exist
    log_dir = Path('logs')
    log_dir.mkdir(exist_ok=True)
    
    # Create a timestamped log file
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = log_dir / f'dataset_test_{timestamp}.log'
    
    # Configure root logger
    log_level = logging.DEBUG if debug else logging.INFO
    
    # Create formatters
    console_format = '%(asctime)s - %(levelname)-8s - %(message)s'
    file_format = '%(asctime)s - %(name)s - %(levelname)-8s - %(message)s'
    
    # Configure root logger with both console and file handlers
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # Remove any existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(logging.Formatter(console_format))
    console_handler.setLevel(log_level)
    root_logger.addHandler(console_handler)
    
    # File handler
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(logging.Formatter(file_format))
    file_handler.setLevel(logging.DEBUG)  # Always log everything to file
    root_logger.addHandler(file_handler)
    
    # Configure paramiko logging
    paramiko_logger = logging.getLogger('paramiko')
    paramiko_logger.setLevel(logging.WARNING)
    
    logger = logging.getLogger(__name__)
    logger.info(f"Logging to {log_file.absolute()}")
    
    return logger

# Global logger instance
logger = setup_logger(debug=args.debug)

logger.info("=" * 80)
logger.info("Starting Remote Dataset Test")
logger.info(f"Log file: {logger.handlers[1].baseFilename}")
logger.info("=" * 80)

# Configuration
CONFIG = {
    'hostname': 'jtmksrv',
    'port': 656,
    'username': 'user',
    'password': '1',
    'remote_path': '/home/<USER>/dataset/test_run',
    'local_download_dir': 'downloaded_datasets'
}

# Expected output files
EXPECTED_FILES = [
    'test_traffic.pcap',
    'test_features.csv',
    'test_flow_features.csv',
    'test_timeline.csv'
]

class DatasetTester:
    """
    Test runner for remote dataset generation and verification.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the DatasetTester with configuration.
        
        Args:
            config: Dictionary containing test configuration
        """
        self.config = config
        self.remote = None
        self.remote_dir = Path(config['remote_path'])
        self.local_download_dir = Path(config.get('local_download_dir', 'downloaded_datasets'))
        self.local_download_dir.mkdir(exist_ok=True, parents=True)
        
        # Expected output files and their minimum sizes (in bytes)
        self.expected_files = {
            'test_traffic.pcap': 1024,  # 1KB minimum
            'test_features.csv': 100,   # 100B minimum
            'test_flow_features.csv': 100,
            'test_timeline.csv': 100,
            'test_output.log': 10
        }
        
        # Test results
        self.results = {
            'start_time': None,
            'end_time': None,
            'success': False,
            'files_found': [],
            'files_missing': [],
            'files_invalid': [],
            'execution_log': []
        }
        
        # Set up logger for this instance
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def _log_execution(self, message: str, level: str = 'info') -> None:
        """
        Log a message with the specified log level and store it in the execution log.
        
        Args:
            message: The message to log
            level: The log level ('debug', 'info', 'warning', 'error', 'critical')
        """
        # Add timestamp to the message
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] {message}"
        
        # Log using the appropriate level
        log_method = getattr(self.logger, level.lower(), self.logger.info)
        log_method(message)
        
        # Also print to console if level is warning or higher
        if level.lower() in ('warning', 'error', 'critical'):
            print(f"[{level.upper()}] {message}")
        
        # Store in execution log
        self.results['execution_log'].append({
            'timestamp': timestamp,
            'level': level.upper(),
            'message': message
        })
    
    def connect(self) -> bool:
        """
        Establish connection to remote server
        
        Returns:
            bool: True if connection was successful, False otherwise
        """
        self._log_execution(f"Connecting to {self.config['username']}@{self.config['hostname']}:{self.config['port']}")
        
        try:
            self.remote = RemoteCLI(
                hostname=self.config['hostname'],
                port=self.config['port'],
                username=self.config['username'],
                password=self.config['password']
            )
            
            if not self.remote.connect():
                self._log_execution("Failed to connect to remote server", 'error')
                return False
                
            # Verify remote directory exists
            _, stdout, _ = self.remote.execute(f'[ -d "{self.remote_dir}" ] && echo "exists" || echo "missing"')
            if 'exists' not in stdout:
                self._log_execution(f"Remote directory {self.remote_dir} does not exist", 'error')
                return False
                
            self._log_execution("Connected to remote server successfully")
            return True
            
        except Exception as e:
            self._log_execution(f"Connection error: {str(e)}", 'error')
            return False
    
    def run_remote_test(self, timeout: int = 300) -> Tuple[bool, str]:
        """
        Run the remote test and dataset generation
        
        Args:
            timeout: Maximum time to wait for test completion (in seconds)
            
        Returns:
            tuple: (success, message) - Whether the test started successfully and a status message
        """
        self._log_execution("Starting remote test execution...")
        
        try:
            # Check if main.py exists
            main_script = self.remote_dir / 'main.py'
            _, stdout, _ = self.remote.execute(f'[ -f "{main_script}" ] && echo "exists" || echo "missing"')
            if 'exists' not in stdout:
                return False, f"Main script not found: {main_script}"
            
            # Run the test in the background
            cmd = f'cd {self.remote_dir} && nohup python3 main.py --config config/test_config.json > test_output.log 2>&1 & echo $!'
            self._log_execution(f"Executing: {cmd}")
            
            # Start the command and get the process ID
            _, stdout, stderr = self.remote.ssh.exec_command(cmd)
            pid = stdout.read().decode().strip()
            
            if not pid.isdigit():
                return False, f"Failed to start process. Error: {stderr.read().decode()}"
            
            self._log_execution(f"Test process started with PID: {pid}")
            return True, pid
            
        except Exception as e:
            error_msg = f"Error starting remote test: {str(e)}"
            self._log_execution(error_msg, 'error')
            return False, error_msg
    
    def wait_for_completion(self, pid: str, timeout: int = 600, poll_interval: int = 10) -> Tuple[bool, str]:
        """
        Wait for the remote process to complete
        
        Args:
            pid: Process ID to monitor
            timeout: Maximum time to wait (in seconds)
            poll_interval: Time between checks (in seconds)
            
        Returns:
            tuple: (success, message) - Whether the process completed successfully and a status message
        """
        self._log_execution(f"Waiting for process {pid} to complete (timeout: {timeout}s)...")
        
        start_time = time.time()
        last_log_time = start_time
        
        try:
            while time.time() - start_time < timeout:
                # Check if process is still running
                _, stdout, _ = self.remote.execute(f'ps -p {pid} > /dev/null 2>&1 && echo "running" || echo "done"')
                status = stdout.strip()
                
                if status == 'done':
                    # Check the exit status
                    _, exit_code, _ = self.remote.execute(f'wait {pid} >/dev/null 2>&1; echo $?')
                    if exit_code.strip() == '0':
                        self._log_execution("Process completed successfully")
                        return True, "Process completed successfully"
                    else:
                        error_msg = f"Process failed with exit code {exit_code.strip()}"
                        self._log_execution(error_msg, 'error')
                        return False, error_msg
                
                # Log progress every 30 seconds
                elapsed = int(time.time() - start_time)
                if time.time() - last_log_time >= 30:
                    self._log_execution(f"Process still running... (elapsed: {elapsed}s)")
                    last_log_time = time.time()
                
                time.sleep(poll_interval)
            
            # If we get here, we timed out
            error_msg = f"Timeout reached after {timeout} seconds. Process {pid} did not complete."
            self._log_execution(error_msg, 'error')
            return False, error_msg
            
        except Exception as e:
            error_msg = f"Error monitoring process {pid}: {str(e)}"
            self._log_execution(error_msg, 'error')
            return False, error_msg
    
    def verify_datasets(self) -> Tuple[bool, List[str]]:
        """
        Verify that all expected dataset files were generated and are valid
        
        Returns:
            tuple: (success, messages) - Whether verification passed and list of status messages
        """
        self._log_execution("Verifying generated datasets...")
        messages = []
        all_success = True
        
        try:
            # Get list of files in the remote directory
            _, stdout, stderr = self.remote.execute(f'ls -la {self.remote_dir}')
            files_list = stdout.split('\n')
            
            # Check for each expected file
            for filename, min_size in self.expected_files.items():
                file_found = any(filename in line for line in files_list)
                
                if not file_found:
                    error_msg = f"Missing expected file: {filename}"
                    self._log_execution(error_msg, 'error')
                    messages.append(error_msg)
                    all_success = False
                    self.results['files_missing'].append(filename)
                    continue
                
                # Check file size
                _, size_out, _ = self.remote.execute(f'stat -c%s {self.remote_dir}/{filename}')
                try:
                    file_size = int(size_out.strip())
                    if file_size < min_size:
                        warn_msg = f"File too small: {filename} (size: {file_size} bytes, min: {min_size} bytes)"
                        self._log_execution(warn_msg, 'warning')
                        messages.append(warn_msg)
                        self.results['files_invalid'].append(filename)
                    else:
                        success_msg = f"Valid file: {filename} (size: {file_size} bytes)"
                        self._log_execution(success_msg)
                        messages.append(success_msg)
                        self.results['files_found'].append(filename)
                except (ValueError, TypeError):
                    error_msg = f"Could not determine size of {filename}"
                    self._log_execution(error_msg, 'error')
                    messages.append(error_msg)
                    all_success = False
            
            # Check for any unexpected files
            all_files = []
            for line in files_list:
                parts = line.split()
                if len(parts) >= 9:  # Typical ls -l output has at least 9 columns
                    all_files.append(' '.join(parts[8:]))
            
            expected_filenames = list(self.expected_files.keys())
            unexpected_files = [f for f in all_files if f not in expected_filenames and f not in ['.', '..']]
            
            if unexpected_files:
                warn_msg = f"Found {len(unexpected_files)} unexpected files: {', '.join(unexpected_files)}"
                self._log_execution(warn_msg, 'warning')
                messages.append(warn_msg)
            
            if all_success:
                self._log_execution("All dataset files verified successfully")
            else:
                self._log_execution("Some dataset files failed verification", 'warning')
            
            return all_success, messages
            
        except Exception as e:
            error_msg = f"Error during dataset verification: {str(e)}"
            self._log_execution(error_msg, 'error')
            return False, [error_msg]
    
    def download_datasets(self) -> Tuple[bool, List[str]]:
        """
        Download the generated datasets from the remote server
        
        Returns:
            tuple: (success, messages) - Whether download was successful and list of status messages
        """
        self._log_execution("Starting dataset download...")
        messages = []
        success_count = 0
        
        try:
            # Ensure local download directory exists
            self.local_download_dir.mkdir(parents=True, exist_ok=True)
            
            # Get list of files to download
            files_to_download = list(self.expected_files.keys()) + ['test_output.log']
            
            for filename in files_to_download:
                remote_path = f"{self.remote_dir}/{filename}"
                local_path = self.local_download_dir / filename
                
                try:
                    # Check if file exists remotely
                    _, stdout, _ = self.remote.execute(f'[ -f "{remote_path}" ] && echo "exists" || echo "missing"')
                    if 'exists' not in stdout:
                        warn_msg = f"Skipping download - file not found: {filename}"
                        self._log_execution(warn_msg, 'warning')
                        messages.append(warn_msg)
                        continue
                    
                    # Download the file
                    self._log_execution(f"Downloading {filename}...")
                    sftp = self.remote.ssh.open_sftp()
                    sftp.get(remote_path, str(local_path))
                    sftp.close()
                    
                    # Verify the downloaded file
                    if local_path.exists() and local_path.stat().st_size > 0:
                        success_msg = f"Downloaded {filename} to {local_path}"
                        self._log_execution(success_msg)
                        messages.append(success_msg)
                        success_count += 1
                    else:
                        error_msg = f"Downloaded file is empty or invalid: {local_path}"
                        self._log_execution(error_msg, 'error')
                        messages.append(error_msg)
                        
                except FileNotFoundError:
                    warn_msg = f"File not found on remote: {filename}"
                    self._log_execution(warn_msg, 'warning')
                    messages.append(warn_msg)
                    
                except Exception as e:
                    error_msg = f"Error downloading {filename}: {str(e)}"
                    self._log_execution(error_msg, 'error')
                    messages.append(error_msg)
            
            # Check if we downloaded all expected files
            all_success = success_count >= len(self.expected_files)
            if all_success:
                self._log_execution("All dataset files downloaded successfully")
            else:
                self._log_execution(f"Downloaded {success_count} out of {len(self.expected_files)} files", 
                                  'warning' if success_count > 0 else 'error')
            
            return all_success, messages
            
        except Exception as e:
            error_msg = f"Error during dataset download: {str(e)}"
            self._log_execution(error_msg, 'error')
            return False, [error_msg]
    
    def run(self, timeout: int = 600) -> bool:
        """
        Run the complete test workflow
        
        Args:
            timeout: Maximum time to wait for test completion (in seconds)
            
        Returns:
            bool: True if all tests passed, False otherwise
        """
        self.results['start_time'] = datetime.now().isoformat()
        self._log_execution("=" * 80)
        self._log_execution("STARTING REMOTE DATASET TEST")
        self._log_execution(f"Remote: {self.config['username']}@{self.config['hostname']}:{self.config['port']}")
        self._log_execution(f"Remote directory: {self.remote_dir}")
        self._log_execution(f"Local download directory: {self.local_download_dir.absolute()}")
        self._log_execution("=" * 80)
        
        try:
            # Step 1: Connect to remote server
            self._log_execution("\n[1/4] Connecting to remote server...")
            if not self.connect():
                self._log_execution("Failed to connect to remote server", 'error')
                return False
            
            # Step 2: Run the remote test
            self._log_execution("\n[2/4] Starting remote test execution...")
            success, result = self.run_remote_test()
            if not success:
                self._log_execution(f"Failed to start remote test: {result}", 'error')
                return False
            
            pid = result  # The result from run_remote_test is the PID
            
            # Step 3: Wait for test completion
            self._log_execution(f"\n[3/4] Waiting for test completion (PID: {pid}, timeout: {timeout}s)...")
            success, message = self.wait_for_completion(pid, timeout=timeout)
            if not success:
                self._log_execution(f"Test execution failed: {message}", 'error')
            
            # Step 4: Verify generated datasets
            self._log_execution("\n[4/4] Verifying generated datasets...")
            verify_success, verify_messages = self.verify_datasets()
            for msg in verify_messages:
                if 'error' in msg.lower():
                    self._log_execution(msg, 'error')
                elif 'warning' in msg.lower():
                    self._log_execution(msg, 'warning')
                else:
                    self._log_execution(msg)
            
            # Step 5: Download datasets
            if not args.no_download:
                self._log_execution("\n[5/5] Downloading datasets...")
                download_success, download_messages = self.download_datasets()
                for msg in download_messages:
                    if 'error' in msg.lower():
                        self._log_execution(msg, 'error')
                    elif 'warning' in msg.lower():
                        self._log_execution(msg, 'warning')
                    else:
                        self._log_execution(msg)
            else:
                self._log_execution("\n[5/5] Skipping dataset download (--no-download flag set)")
                download_success = True
            
            # Determine overall success
            self.results['success'] = success and verify_success and download_success
            self.results['end_time'] = datetime.now().isoformat()
            
            # Log final status
            self._log_execution("\n" + "=" * 80)
            self._log_execution("TEST EXECUTION COMPLETE")
            self._log_execution(f"Status: {'SUCCESS' if self.results['success'] else 'FAILED'}")
            self._log_execution(f"Start time: {self.results['start_time']}")
            self._log_execution(f"End time: {self.results['end_time']}")
            self._log_execution("=" * 80)
            
            return self.results['success']
            
        except Exception as e:
            error_msg = f"Unexpected error during test execution: {str(e)}"
            self._log_execution(error_msg, 'error')
            self.results['error'] = error_msg
            return False
            
        finally:
            # Clean up if requested
            if args.cleanup and 'remote' in self.__dict__ and self.remote:
                self._log_execution("\nCleaning up remote files...")
                try:
                    for filename in self.expected_files.keys():
                        self.remote.execute(f'rm -f {self.remote_dir}/{filename}')
                    self._log_execution("Cleanup completed")
                except Exception as e:
                    self._log_execution(f"Error during cleanup: {str(e)}", 'error')
            
            # Close the connection
            if 'remote' in self.__dict__ and self.remote:
                self.remote.close()
                self._log_execution("Closed connection to remote server")

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Test remote dataset generation and verification')
    
    # Connection settings
    parser.add_argument('--host', type=str, default='jtmksrv',
                      help='Remote server hostname or IP (default: jtmksrv)')
    parser.add_argument('--port', type=int, default=656,
                      help='SSH port number (default: 656)')
    parser.add_argument('--username', type=str, default='user',
                      help='SSH username (default: user)')
    parser.add_argument('--password', type=str, default='1',
                      help='SSH password (default: 1, use with caution, better to use SSH keys)')
    
    # Test settings
    parser.add_argument('--remote-dir', type=str, default='/home/<USER>/dataset/test_run',
                      help='Remote directory containing the dataset generation code (default: /home/<USER>/dataset/test_run)')
    parser.add_argument('--local-dir', type=str, default='downloaded_datasets',
                      help='Local directory to download datasets to (default: downloaded_datasets)')
    parser.add_argument('--timeout', type=int, default=600,
                      help='Maximum time to wait for test completion in seconds (default: 600)')
    
    # Options
    parser.add_argument('--no-download', action='store_true',
                      help='Skip downloading generated datasets')
    parser.add_argument('--cleanup', action='store_true',
                      help='Clean up remote files after test')
    parser.add_argument('--debug', action='store_true',
                      help='Enable debug logging')
    
    return parser.parse_args()

def save_test_report(tester: 'DatasetTester', output_dir: str = 'test_reports') -> Path:
    """
    Save test results to a JSON report file
    
    Args:
        tester: The DatasetTester instance with test results
        output_dir: Directory to save the report to
        
    Returns:
        Path to the saved report file
    """
    # Create output directory if it doesn't exist
    report_dir = Path(output_dir)
    report_dir.mkdir(exist_ok=True, parents=True)
    
    # Create a timestamped report filename
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = report_dir / f'dataset_test_report_{timestamp}.json'
    
    # Prepare report data
    report_data = {
        'test_run': {
            'start_time': tester.results.get('start_time'),
            'end_time': tester.results.get('end_time'),
            'success': tester.results.get('success', False),
            'duration_seconds': None
        },
        'files': {
            'found': tester.results.get('files_found', []),
            'missing': tester.results.get('files_missing', []),
            'invalid': tester.results.get('files_invalid', [])
        },
        'execution_log': tester.results.get('execution_log', [])
    }
    
    # Calculate duration if both start and end times are available
    if tester.results.get('start_time') and tester.results.get('end_time'):
        try:
            start = datetime.fromisoformat(tester.results['start_time'])
            end = datetime.fromisoformat(tester.results['end_time'])
            report_data['test_run']['duration_seconds'] = (end - start).total_seconds()
        except (ValueError, TypeError):
            pass
    
    # Save report to file
    with open(report_file, 'w') as f:
        json.dump(report_data, f, indent=2, default=str)
    
    return report_file

def main() -> int:
    """
    Main function to run the remote dataset test
    
    Returns:
        int: Exit code (0 = success, 1 = test failed, 2 = error)
    """
    # Parse command line arguments
    args = parse_args()
    
    # Configure logging
    logger = setup_logger(debug=args.debug)
    
    try:
        # Log startup information
        logger.info("=" * 80)
        logger.info("REMOTE DATASET TEST")
        logger.info("-" * 40)
        logger.info(f"Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"Python: {sys.version.split()[0]} on {sys.platform}")
        logger.info(f"Working Directory: {Path.cwd()}")
        logger.info("=" * 80 + "\n")
        
        # Create test configuration
        config = {
            'hostname': args.host,
            'port': args.port,
            'username': args.username,
            'password': args.password,
            'remote_path': args.remote_dir,
            'local_download_dir': args.local_dir
        }
        
        # Log configuration
        logger.info("TEST CONFIGURATION")
        logger.info("-" * 20)
        logger.info(f"Remote Server: {args.username}@{args.host}:{args.port}")
        logger.info(f"Remote Directory: {args.remote_dir}")
        logger.info(f"Local Download Directory: {Path(args.local_dir).absolute()}")
        logger.info(f"Timeout: {args.timeout} seconds")
        logger.info(f"Download Datasets: {'No' if args.no_download else 'Yes'}")
        logger.info(f"Cleanup Remote: {'Yes' if args.cleanup else 'No'}")
        logger.info("\n" + "=" * 80 + "\n")
        
        # Create and run the tester
        logger.info("Starting dataset test...")
        tester = DatasetTester(config)
        success = tester.run(timeout=args.timeout)
        
        # Save test report
        report_file = save_test_report(tester)
        
        # Log final result
        logger.info("\n" + "=" * 80)
        if success:
            logger.info("✅ TEST COMPLETED SUCCESSFULLY")
        else:
            logger.error("❌ TEST FAILED")
        
        logger.info(f"Test report saved to: {report_file.absolute()}")
        logger.info("=" * 80)
        
        return 0 if success else 1
            
    except KeyboardInterrupt:
        logger.warning("\nTest interrupted by user")
        return 130  # Standard exit code for Ctrl+C
        
    except Exception as e:
        logger.critical(f"❌ CRITICAL ERROR: {str(e)}", exc_info=args.debug)
        return 2
        
    finally:
        # Ensure all logs are flushed
        for handler in logging.root.handlers:
            handler.flush()
            if hasattr(handler, 'close'):
                handler.close()
        
        # Close any open SSH connections
        if 'tester' in locals() and hasattr(tester, 'remote') and tester.remote:
            try:
                tester.remote.close()
                logger.debug("Closed SSH connection")
            except Exception as e:
                logger.warning(f"Error closing SSH connection: {e}")

if __name__ == "__main__":
    sys.exit(main())
