#!/usr/bin/env python3
import os
import re

# Configuration
CONFIG = {
    'hostname': 'jtmksrv',
    'port': 656,
    'username': 'user',
    'password': '1',
    'remote_path': '/home/<USER>/dataset/test_run'
}

def update_file(file_path):
    """Update configuration in a file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Update hostname
    content = re.sub(r"hostname\s*=\s*['\"].*?['\"]", 
                    f"hostname = '{CONFIG['hostname']}'", content)
    
    # Update port
    content = re.sub(r"port\s*=\s*\d+", 
                    f"port = {CONFIG['port']}", content)
    
    # Update username
    content = re.sub(r"username\s*=\s*['\"].*?['\"]", 
                    f"username = '{CONFIG['username']}'", content)
    
    # Update password
    content = re.sub(r"password\s*=\s*['\"].*?['\"]", 
                    f"password = '{CONFIG['password']}'", content)
    
    # Update remote path
    content = re.sub(r"remote_path\s*=\s*['\"].*?['\"]", 
                    f"remote_path = '{CONFIG['remote_path']}'", content)
    
    # Update in dictionary format
    content = re.sub(r"'hostname'\s*:\s*['\"].*?['\"]", 
                    f"'hostname': '{CONFIG['hostname']}'", content)
    content = re.sub(r"'port'\s*:\s*\d+", 
                    f"'port': {CONFIG['port']}", content)
    content = re.sub(r"'username'\s*:\s*['\"].*?['\"]", 
                    f"'username': '{CONFIG['username']}'", content)
    content = re.sub(r"'password'\s*:\s*['\"].*?['\"]", 
                    f"'password': '{CONFIG['password']}'", content)
    content = re.sub(r"'remote_path'\s*:\s*['\"].*?['\"]", 
                    f"'remote_path': '{CONFIG['remote_path']}'", content)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Updated {file_path}")

def main():
    # Get all Python files in the test directory
    test_dir = os.path.dirname(os.path.abspath(__file__))
    for file in os.listdir(test_dir):
        if file.endswith('.py') and file != 'update_test_configs.py':
            file_path = os.path.join(test_dir, file)
            update_file(file_path)
    
    print("\nAll test files have been updated with the following configuration:")
    print(f"Hostname: {CONFIG['hostname']}")
    print(f"Port: {CONFIG['port']}")
    print(f"Username: {CONFIG['username']}")
    print(f"Password: {'*' * len(CONFIG['password'])}")
    print(f"Remote Path: {CONFIG['remote_path']}")

if __name__ == "__main__":
    main()
